# Exemples d'Utilisation - ICOM IC-R8600 Controller

## 🚀 Démarrage Rapide

### 1. Installation et Lancement
```bash
# Cloner le projet
git clone <repository-url>
cd icom-controller

# Lancer automatiquement (Windows)
start.bat

# Ou lancer automatiquement (Linux/Mac)
chmod +x start.sh
./start.sh
```

### 2. Configuration Initiale
1. Connecter l'IC-R8600 via USB ou réseau
2. Modifier le port dans `backend/main.py` (ligne 48)
3. Vérifier la connexion avec `python backend/test_icom.py`

## 📡 Exemples de Contrôle

### Changement de Fréquence
```javascript
// Via l'interface web
frequency: 145500000  // 145.5 MHz
mode: "FM"
rf_gain: 128

// Via API REST
curl -X POST "http://localhost:8000/api/command" \
  -H "Content-Type: application/json" \
  -d '{
    "frequency": 145500000,
    "mode": "FM",
    "rf_gain": 128
  }'
```

### Contrôle d'Alimentation
```javascript
// Allumer
curl -X POST "http://localhost:8000/api/command" \
  -H "Content-Type: application/json" \
  -d '{"power_on": true}'

// Éteindre
curl -X POST "http://localhost:8000/api/command" \
  -H "Content-Type: application/json" \
  -d '{"power_on": false}'
```

### Lecture d'État
```javascript
// État complet
curl "http://localhost:8000/api/status"

// Réponse exemple
{
  "frequency": 145500000,
  "mode": "FM",
  "rssi": 120,
  "power_on": true,
  "rf_gain": 128,
  "filter_width": 0,
  "timestamp": "2024-01-01T12:00:00"
}
```

## 🔍 Exemples de Scan

### Scan Bande Amateur 2m
```javascript
// Via l'interface web
start_frequency: 144000000  // 144 MHz
end_frequency: 146000000    // 146 MHz
step: 25000                 // 25 kHz
mode: "FM"

// Via API
curl -X POST "http://localhost:8000/api/scan/start" \
  -H "Content-Type: application/json" \
  -d '{
    "start_frequency": 144000000,
    "end_frequency": 146000000,
    "step": 25000,
    "mode": "FM"
  }'
```

### Scan Aviation
```javascript
{
  "start_frequency": 118000000,  // 118 MHz
  "end_frequency": 137000000,    // 137 MHz
  "step": 25000,                 // 25 kHz
  "mode": "AM"
}
```

### Arrêt du Scan
```javascript
curl -X POST "http://localhost:8000/api/scan/stop"
```

## 🎵 Exemples d'Enregistrement

### Démarrer Enregistrement AF
```javascript
// Via l'interface web
audio_type: "AF"  // Audio Frequency

// Via API
curl -X POST "http://localhost:8000/api/audio/start" \
  -H "Content-Type: application/json" \
  -d '{"audio_type": "AF"}'
```

### Démarrer Enregistrement IF
```javascript
{
  "audio_type": "IF",  // Intermediate Frequency
  "device_id": 1       // Périphérique audio spécifique
}
```

### Arrêter Enregistrement
```javascript
curl -X POST "http://localhost:8000/api/audio/stop"

// Réponse
{
  "success": true,
  "message": "Enregistrement terminé et sauvegardé",
  "filename": "icom_r8600_AF_20240101_120000.wav",
  "duration": 30.5,
  "file_size": 2940000
}
```

### Lister les Enregistrements
```javascript
curl "http://localhost:8000/api/recordings"

// Réponse
[
  {
    "filename": "icom_r8600_AF_20240101_120000.wav",
    "path": "recordings/icom_r8600_AF_20240101_120000.wav",
    "size": 2940000,
    "created": "2024-01-01T12:00:00",
    "duration": 30.5,
    "sample_rate": 48000
  }
]
```

### Télécharger un Enregistrement
```javascript
// URL directe
http://localhost:8000/api/recordings/icom_r8600_AF_20240101_120000.wav

// Avec curl
curl -O "http://localhost:8000/api/recordings/icom_r8600_AF_20240101_120000.wav"
```

## 🔧 Exemples de Configuration

### Configuration Série
```python
# backend/main.py
icom_handler = ICOMHandler(
    port="COM3",        # Windows
    # port="/dev/ttyUSB0",  # Linux
    baudrate=19200,
    use_udp=False
)
```

### Configuration UDP
```python
icom_handler = ICOMHandler(
    use_udp=True,
    udp_host="*************",
    udp_port=50001
)
```

### Configuration Audio
```python
audio_recorder = AudioRecorder(
    sample_rate=48000,
    channels=1,
    recordings_dir="recordings"
)
```

## 📱 Exemples d'Interface

### Fréquences Prédéfinies
```javascript
const presets = [
  { name: "2m FM Repeater", freq: 145500000, mode: "FM" },
  { name: "70cm FM Repeater", freq: 433500000, mode: "FM" },
  { name: "Aviation", freq: 121500000, mode: "AM" },
  { name: "Marine VHF", freq: 156800000, mode: "FM" },
  { name: "PMR446", freq: 446000000, mode: "FM" }
];
```

### Ranges de Scan Prédéfinis
```javascript
const scanRanges = [
  {
    name: "2m Amateur",
    start: 144000000,
    end: 146000000,
    step: 25000,
    mode: "FM"
  },
  {
    name: "70cm Amateur",
    start: 430000000,
    end: 440000000,
    step: 25000,
    mode: "FM"
  }
];
```

## 🐛 Exemples de Dépannage

### Test de Connexion
```bash
cd backend
python test_icom.py
```

### Test Manuel des Commandes
```python
from icom_handler import ICOMHandler

# Créer connexion
handler = ICOMHandler(port="COM3")
handler.connect()

# Test fréquence
handler.set_frequency(145500000)
freq = handler.get_frequency()
print(f"Fréquence: {freq}")

# Test mode
handler.set_mode("FM")

# Test RSSI
rssi = handler.get_rssi()
print(f"RSSI: {rssi}")

# Fermer
handler.disconnect()
```

### Vérification Audio
```python
import sounddevice as sd

# Lister périphériques
print(sd.query_devices())

# Test enregistrement
import numpy as np
duration = 5  # secondes
fs = 48000
recording = sd.rec(int(duration * fs), samplerate=fs, channels=1)
sd.wait()
print(f"Enregistré {len(recording)} échantillons")
```

## 🌐 Intégration avec d'Autres Systèmes

### MQTT Publishing
```python
# Ajouter dans icom_handler.py
import paho.mqtt.client as mqtt

def publish_status(self, status):
    client = mqtt.Client()
    client.connect("localhost", 1883, 60)
    client.publish("icom/r8600/status", json.dumps(status))
    client.disconnect()
```

### Webhook Notifications
```python
# Ajouter dans audio_recorder.py
import requests

def notify_recording_complete(self, filename):
    webhook_url = "https://your-webhook.com/notify"
    data = {
        "event": "recording_complete",
        "filename": filename,
        "timestamp": datetime.now().isoformat()
    }
    requests.post(webhook_url, json=data)
```

### Base de Données des Enregistrements
```python
import sqlite3

def save_recording_metadata(filename, duration, frequency, mode):
    conn = sqlite3.connect('recordings.db')
    cursor = conn.cursor()
    cursor.execute('''
        INSERT INTO recordings (filename, duration, frequency, mode, timestamp)
        VALUES (?, ?, ?, ?, ?)
    ''', (filename, duration, frequency, mode, datetime.now()))
    conn.commit()
    conn.close()
```
