#!/usr/bin/env python3
"""
Test des nouveaux ports COM6 et COM7 avec adresse 00h
"""

import serial
import time

def test_port(port_name):
    """Test un port COM spécifique"""
    print(f"\n=== Test {port_name} ===")
    
    try:
        ser = serial.Serial(port_name, 19200, timeout=2.0)
        print(f"✅ {port_name} ouvert")
        
        # Test 1: Lecture fréquence avec 00h
        cmd = bytes([0xFE, 0xFE, 0x96, 0x00, 0x03, 0xFD])
        print(f"Envoi lecture fréquence: {' '.join([f'{b:02X}' for b in cmd])}")
        
        ser.write(cmd)
        time.sleep(0.5)
        
        response = ser.read(50)
        if response:
            print(f"🎉 RÉPONSE {port_name}: {' '.join([f'{b:02X}' for b in response])}")
            print(f"   Longueur: {len(response)} bytes")
            return True
        else:
            print(f"❌ Pas de réponse sur {port_name}")
        
        # Test 2: Power status
        cmd = bytes([0xFE, 0xFE, 0x96, 0x00, 0x18, 0x00, 0xFD])
        print(f"Envoi power status: {' '.join([f'{b:02X}' for b in cmd])}")
        
        ser.write(cmd)
        time.sleep(0.5)
        
        response = ser.read(50)
        if response:
            print(f"🎉 RÉPONSE Power: {' '.join([f'{b:02X}' for b in response])}")
        else:
            print(f"❌ Pas de réponse Power sur {port_name}")
        
        ser.close()
        return response is not None
        
    except Exception as e:
        print(f"❌ Erreur {port_name}: {e}")
        return False

def test_all_ports():
    """Test tous les ports disponibles"""
    ports = ['COM3', 'COM6', 'COM7']
    working_ports = []
    
    for port in ports:
        if test_port(port):
            working_ports.append(port)
    
    return working_ports

def test_advanced_commands(port):
    """Test des commandes avancées sur le port qui fonctionne"""
    print(f"\n=== Test Commandes Avancées sur {port} ===")
    
    try:
        ser = serial.Serial(port, 19200, timeout=2.0)
        
        commands = [
            ([0xFE, 0xFE, 0x96, 0x00, 0x03, 0xFD], "Lecture fréquence"),
            ([0xFE, 0xFE, 0x96, 0x00, 0x04, 0xFD], "Lecture mode"),
            ([0xFE, 0xFE, 0x96, 0x00, 0x15, 0x02, 0xFD], "Lecture RSSI"),
            ([0xFE, 0xFE, 0x96, 0x00, 0x18, 0x00, 0xFD], "Power status"),
        ]
        
        for cmd_bytes, description in commands:
            print(f"\n--- {description} ---")
            cmd = bytes(cmd_bytes)
            print(f"Envoi: {' '.join([f'{b:02X}' for b in cmd])}")
            
            ser.write(cmd)
            time.sleep(0.3)
            
            response = ser.read(50)
            if response:
                print(f"✅ Réponse: {' '.join([f'{b:02X}' for b in response])}")
                
                # Analyse de la réponse
                if len(response) >= 6:
                    if response[0:2] == bytes([0xFE, 0xFE]):
                        print(f"   Format CI-V valide")
                        if response[2] == 0x00 and response[3] == 0x96:
                            print(f"   Réponse de l'IC-R8600 ✅")
                        else:
                            print(f"   Adresses: {response[2]:02X}h → {response[3]:02X}h")
            else:
                print(f"❌ Pas de réponse")
        
        ser.close()
        
    except Exception as e:
        print(f"❌ Erreur test avancé: {e}")

if __name__ == "__main__":
    print("Test des Nouveaux Ports COM IC-R8600")
    print("====================================")
    
    # Test de tous les ports
    working_ports = test_all_ports()
    
    print(f"\n📊 RÉSUMÉ:")
    print(f"Ports fonctionnels: {working_ports}")
    
    if working_ports:
        # Test approfondi sur le premier port qui fonctionne
        best_port = working_ports[0]
        print(f"\n🎯 Port recommandé: {best_port}")
        
        test_advanced_commands(best_port)
        
        print(f"\n✅ CONFIGURATION RECOMMANDÉE:")
        print(f"Port série: {best_port}")
        print(f"Adresse IC-R8600: 96h")
        print(f"Adresse contrôleur: 00h")
        print(f"Baud rate: 19200")
        
        # Mise à jour du fichier de configuration
        try:
            import json
            
            with open("backend/config.json", "r") as f:
                config = json.load(f)
            
            config["icom"]["serial_port"] = best_port
            config["icom"]["controller_address"] = "0x00"
            
            with open("backend/config.json", "w") as f:
                json.dump(config, f, indent=2)
            
            print(f"✅ Configuration mise à jour automatiquement")
            
        except Exception as e:
            print(f"⚠️ Erreur mise à jour config: {e}")
    
    else:
        print(f"\n❌ Aucun port ne répond aux commandes CI-V")
        print(f"Vérifiez la configuration IC-R8600:")
        print(f"- CI-V Transceive: ON")
        print(f"- USB REAR Port: Link to Remote")
        print(f"- Transceive Address: 00h")
