import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { <PERSON>lider } from './ui/slider';
import { Badge } from './ui/badge';
import {
  Power,
  Radio,
  Volume2,
  Settings,
  Zap,
  Activity,
  Gauge,
  Waves,
  Target,
  Lock,
  Unlock,
  Wifi,
  WifiOff,
  Clock,
  Cpu
} from 'lucide-react';
import AudioStreamControl from './AudioStreamControl';

const ProfessionalRadioControl = () => {
  // États du récepteur
  const [radioStatus, setRadioStatus] = useState({
    power_on: false,
    frequency: 145000000,
    mode: 'FM',
    rf_gain: 50,
    rssi: -80,
    filter_width: 15000,
    squelch: 0,
    volume: 50
  });

  // États de l'interface
  const [isConnected, setIsConnected] = useState(false);
  const [isLocked, setIsLocked] = useState(false);
  const [frequencyInput, setFrequencyInput] = useState('145.000.000');
  const [lastCommand, setLastCommand] = useState('');
  const [commandCount, setCommandCount] = useState(0);
  const [latency, setLatency] = useState(0);
  const [connectionQuality, setConnectionQuality] = useState('EXCELLENT');

  // Références pour optimisation
  const wsRef = useRef(null);
  const statusIntervalRef = useRef(null);
  const commandQueueRef = useRef([]);
  const isProcessingRef = useRef(false);

  // WebSocket ultra-rapide
  const connectWebSocket = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) return;

    wsRef.current = new WebSocket('ws://localhost:8000/ws');

    wsRef.current.onopen = () => {
      console.log('🚀 Connexion WebSocket établie - Mode ULTRA-RAPIDE');
      setIsConnected(true);
      setConnectionQuality('EXCELLENT');
      startStatusUpdates();
    };

    wsRef.current.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        if (data.type === 'status') {
          setRadioStatus(prev => ({ ...prev, ...data.data }));

          // Mise à jour latence et qualité
          if (data.data.latency_ms) {
            setLatency(data.data.latency_ms);
            if (data.data.latency_ms < 10) setConnectionQuality('EXCELLENT');
            else if (data.data.latency_ms < 50) setConnectionQuality('GOOD');
            else if (data.data.latency_ms < 100) setConnectionQuality('FAIR');
            else setConnectionQuality('POOR');
          }
        }
      } catch (error) {
        console.error('Erreur parsing WebSocket:', error);
      }
    };

    wsRef.current.onclose = () => {
      console.log('❌ Connexion WebSocket fermée');
      setIsConnected(false);
      setConnectionQuality('DISCONNECTED');
      setTimeout(connectWebSocket, 1000);
    };

    wsRef.current.onerror = (error) => {
      console.error('Erreur WebSocket:', error);
      setConnectionQuality('ERROR');
    };
  }, []);

  // Commande instantanée optimisée
  const sendCommand = useCallback(async (endpoint, data = {}) => {
    if (isLocked) return;

    const command = { endpoint, data, timestamp: Date.now() };
    setLastCommand(`${endpoint}: ${JSON.stringify(data)}`);
    setCommandCount(prev => prev + 1);

    try {
      const response = await fetch(`http://localhost:8000/${endpoint}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      // Mise à jour immédiate de l'interface (optimiste)
      if (endpoint === 'frequency' && data.frequency) {
        setRadioStatus(prev => ({ ...prev, frequency: data.frequency }));
        setFrequencyInput(formatFrequency(data.frequency));
      } else if (endpoint === 'mode' && data.mode) {
        setRadioStatus(prev => ({ ...prev, mode: data.mode }));
      } else if (endpoint === 'rf_gain' && data.gain !== undefined) {
        setRadioStatus(prev => ({ ...prev, rf_gain: data.gain }));
      }

    } catch (error) {
      console.error(`❌ Erreur commande ${endpoint}:`, error);
    }
  }, [isLocked]);

  // Formatage fréquence
  const formatFrequency = (freq) => {
    const freqStr = freq.toString().padStart(9, '0');
    return `${freqStr.slice(0, 3)}.${freqStr.slice(3, 6)}.${freqStr.slice(6, 9)}`;
  };

  // Parsing fréquence
  const parseFrequency = (freqStr) => {
    return parseInt(freqStr.replace(/\./g, ''));
  };

  // Contrôles fréquence ultra-rapides
  const handleFrequencyChange = useCallback((delta) => {
    const newFreq = radioStatus.frequency + delta;
    if (newFreq >= 100000 && newFreq <= 3000000000) {
      sendCommand('frequency', { frequency: newFreq });
    }
  }, [radioStatus.frequency, sendCommand]);

  // Contrôles clavier
  useEffect(() => {
    const handleKeyPress = (e) => {
      if (isLocked || e.target.tagName === 'INPUT') return;

      switch (e.key) {
        case 'ArrowUp':
          e.preventDefault();
          handleFrequencyChange(e.shiftKey ? 1000000 : e.ctrlKey ? 100000 : 25000);
          break;
        case 'ArrowDown':
          e.preventDefault();
          handleFrequencyChange(e.shiftKey ? -1000000 : e.ctrlKey ? -100000 : -25000);
          break;
        case 'ArrowLeft':
          e.preventDefault();
          handleFrequencyChange(e.shiftKey ? -10000 : -1000);
          break;
        case 'ArrowRight':
          e.preventDefault();
          handleFrequencyChange(e.shiftKey ? 10000 : 1000);
          break;
        case ' ':
          e.preventDefault();
          sendCommand('power', { state: !radioStatus.power_on });
          break;
        case 'l':
          setIsLocked(!isLocked);
          break;
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [handleFrequencyChange, radioStatus.power_on, sendCommand, isLocked]);

  // Mise à jour du statut
  const startStatusUpdates = useCallback(() => {
    if (statusIntervalRef.current) return;
    
    statusIntervalRef.current = setInterval(async () => {
      try {
        const response = await fetch('http://localhost:8000/status');
        if (response.ok) {
          const status = await response.json();
          setRadioStatus(prev => ({ ...prev, ...status }));
        }
      } catch (error) {
        console.error('Erreur mise à jour statut:', error);
      }
    }, 100); // Mise à jour ultra-rapide 10Hz
  }, []);

  // Initialisation
  useEffect(() => {
    connectWebSocket();
    return () => {
      if (wsRef.current) wsRef.current.close();
      if (statusIntervalRef.current) clearInterval(statusIntervalRef.current);
    };
  }, [connectWebSocket]);

  // Modes disponibles
  const modes = ['LSB', 'USB', 'AM', 'FM', 'WFM', 'CW', 'RTTY', 'DV'];

  return (
    <div className="min-h-screen bg-gray-900 text-white p-4">
      {/* Header Professionnel */}
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Radio className="w-8 h-8 text-blue-400" />
          <h1 className="text-2xl font-bold">ICOM IC-R8600 - CONTRÔLE PROFESSIONNEL</h1>
          <Badge variant={isConnected ? "default" : "destructive"}>
            {isConnected ? "CONNECTÉ" : "DÉCONNECTÉ"}
          </Badge>
        </div>
        
        <div className="flex items-center space-x-2">
          <Badge variant={connectionQuality === 'EXCELLENT' ? 'default' :
                         connectionQuality === 'GOOD' ? 'secondary' :
                         connectionQuality === 'FAIR' ? 'outline' : 'destructive'}>
            {isConnected ? <Wifi className="w-3 h-3 mr-1" /> : <WifiOff className="w-3 h-3 mr-1" />}
            {connectionQuality}
          </Badge>
          <Badge variant="outline">
            <Clock className="w-3 h-3 mr-1" />
            {latency.toFixed(1)}ms
          </Badge>
          <Button
            variant={isLocked ? "destructive" : "outline"}
            size="sm"
            onClick={() => setIsLocked(!isLocked)}
          >
            {isLocked ? <Lock className="w-4 h-4" /> : <Unlock className="w-4 h-4" />}
            {isLocked ? "VERROUILLÉ" : "DÉVERROUILLÉ"}
          </Button>
          <Badge variant="outline">
            <Cpu className="w-3 h-3 mr-1" />
            CMD: {commandCount}
          </Badge>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Contrôle Principal */}
        <Card className="lg:col-span-2 bg-gray-800 border-gray-700">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Target className="w-5 h-5 text-green-400" />
              <span>CONTRÔLE PRINCIPAL</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Alimentation */}
            <div className="flex items-center justify-between">
              <Button
                variant={radioStatus.power_on ? "default" : "outline"}
                size="lg"
                onClick={() => sendCommand('power', { state: !radioStatus.power_on })}
                disabled={isLocked}
                className="w-32"
              >
                <Power className="w-5 h-5 mr-2" />
                {radioStatus.power_on ? "ON" : "OFF"}
              </Button>
              
              <div className="text-right">
                <div className="text-sm text-gray-400">RSSI</div>
                <div className="text-xl font-mono text-green-400">
                  {radioStatus.rssi} dBm
                </div>
              </div>
            </div>

            {/* Fréquence */}
            <div className="space-y-4">
              <div className="text-center">
                <div className="text-sm text-gray-400 mb-2">FRÉQUENCE (Hz)</div>
                <div className="text-4xl font-mono text-blue-400 mb-4">
                  {formatFrequency(radioStatus.frequency)}
                </div>
                
                {/* Contrôles fréquence rapides */}
                <div className="grid grid-cols-5 gap-2 mb-4">
                  <Button size="sm" onClick={() => handleFrequencyChange(-1000000)} disabled={isLocked}>-1M</Button>
                  <Button size="sm" onClick={() => handleFrequencyChange(-100000)} disabled={isLocked}>-100k</Button>
                  <Button size="sm" onClick={() => handleFrequencyChange(-25000)} disabled={isLocked}>-25k</Button>
                  <Button size="sm" onClick={() => handleFrequencyChange(25000)} disabled={isLocked}>+25k</Button>
                  <Button size="sm" onClick={() => handleFrequencyChange(100000)} disabled={isLocked}>+100k</Button>
                </div>
                
                <Input
                  value={frequencyInput}
                  onChange={(e) => setFrequencyInput(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      const freq = parseFrequency(frequencyInput);
                      if (freq >= 100000 && freq <= 3000000000) {
                        sendCommand('frequency', { frequency: freq });
                      }
                    }
                  }}
                  className="text-center font-mono"
                  placeholder="000.000.000"
                  disabled={isLocked}
                />
              </div>
            </div>

            {/* Mode */}
            <div className="space-y-2">
              <div className="text-sm text-gray-400">MODE</div>
              <div className="grid grid-cols-4 gap-2">
                {modes.map(mode => (
                  <Button
                    key={mode}
                    variant={radioStatus.mode === mode ? "default" : "outline"}
                    size="sm"
                    onClick={() => sendCommand('mode', { mode })}
                    disabled={isLocked}
                  >
                    {mode}
                  </Button>
                ))}
              </div>
            </div>

            {/* RF Gain */}
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-gray-400">RF GAIN</span>
                <span className="text-sm font-mono text-green-400">{radioStatus.rf_gain}%</span>
              </div>
              <Slider
                value={[radioStatus.rf_gain]}
                onValueChange={([value]) => sendCommand('rf_gain', { gain: value })}
                max={100}
                step={1}
                className="w-full"
                disabled={isLocked}
              />
            </div>
          </CardContent>
        </Card>

        {/* Statut et Contrôles Avancés */}
        <Card className="bg-gray-800 border-gray-700">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Activity className="w-5 h-5 text-yellow-400" />
              <span>STATUT SYSTÈME</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Indicateurs */}
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm">Alimentation:</span>
                <Badge variant={radioStatus.power_on ? "default" : "secondary"}>
                  {radioStatus.power_on ? "ON" : "OFF"}
                </Badge>
              </div>
              
              <div className="flex justify-between">
                <span className="text-sm">Mode:</span>
                <Badge variant="outline">{radioStatus.mode}</Badge>
              </div>
              
              <div className="flex justify-between">
                <span className="text-sm">RSSI:</span>
                <span className="font-mono text-green-400">{radioStatus.rssi} dBm</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-sm">RF Gain:</span>
                <span className="font-mono text-blue-400">{radioStatus.rf_gain}%</span>
              </div>
            </div>

            {/* Dernière commande */}
            <div className="mt-6 p-3 bg-gray-700 rounded">
              <div className="text-xs text-gray-400 mb-1">DERNIÈRE COMMANDE:</div>
              <div className="text-xs font-mono text-green-400 break-all">
                {lastCommand || 'Aucune'}
              </div>
            </div>

            {/* Raccourcis clavier */}
            <div className="mt-6 p-3 bg-gray-700 rounded">
              <div className="text-xs text-gray-400 mb-2">RACCOURCIS:</div>
              <div className="text-xs space-y-1">
                <div>↑↓: ±25kHz (Shift: ±1MHz)</div>
                <div>←→: ±1kHz (Shift: ±10kHz)</div>
                <div>ESPACE: Power ON/OFF</div>
                <div>L: Verrouiller/Déverrouiller</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Contrôle Audio Temps Réel */}
        <AudioStreamControl
          radioStatus={radioStatus}
          onVolumeChange={(volume) => {
            setRadioStatus(prev => ({ ...prev, volume }));
          }}
        />
      </div>
    </div>
  );
};

export default ProfessionalRadioControl;
