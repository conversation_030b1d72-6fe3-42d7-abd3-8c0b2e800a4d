<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test WebSocket ICOM</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .connected { background: #2d5a2d; }
        .disconnected { background: #5a2d2d; }
        .error { background: #5a4d2d; }
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #4a4a4a;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover { background: #6a6a6a; }
        #log {
            background: #2a2a2a;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🚀 Test WebSocket ICOM IC-R8600</h1>
    
    <div id="status" class="status disconnected">
        ❌ Déconnecté
    </div>
    
    <div>
        <button onclick="connectWS()">Se connecter</button>
        <button onclick="disconnectWS()">Se déconnecter</button>
        <button onclick="sendTestCommand()">Envoyer commande test</button>
        <button onclick="clearLog()">Effacer log</button>
    </div>
    
    <h3>Log des événements :</h3>
    <div id="log"></div>
    
    <script>
        let ws = null;
        let reconnectInterval = null;
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function updateStatus(status, className) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = status;
            statusDiv.className = `status ${className}`;
        }
        
        function connectWS() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                log('⚠️ Déjà connecté');
                return;
            }
            
            log('🔄 Tentative de connexion à ws://localhost:8000/ws');
            
            try {
                ws = new WebSocket('ws://localhost:8000/ws');
                
                ws.onopen = function(event) {
                    log('✅ Connexion WebSocket établie');
                    updateStatus('✅ Connecté', 'connected');
                    clearInterval(reconnectInterval);
                };
                
                ws.onmessage = function(event) {
                    log(`📨 Message reçu: ${event.data}`);
                };
                
                ws.onclose = function(event) {
                    log(`❌ Connexion fermée (Code: ${event.code}, Raison: ${event.reason})`);
                    updateStatus('❌ Déconnecté', 'disconnected');
                    
                    // Reconnexion automatique
                    if (!reconnectInterval) {
                        reconnectInterval = setInterval(() => {
                            log('🔄 Tentative de reconnexion...');
                            connectWS();
                        }, 2000);
                    }
                };
                
                ws.onerror = function(error) {
                    log(`🚨 Erreur WebSocket: ${error}`);
                    updateStatus('🚨 Erreur', 'error');
                };
                
            } catch (error) {
                log(`🚨 Erreur lors de la création du WebSocket: ${error}`);
                updateStatus('🚨 Erreur', 'error');
            }
        }
        
        function disconnectWS() {
            if (ws) {
                clearInterval(reconnectInterval);
                reconnectInterval = null;
                ws.close();
                ws = null;
                log('🔌 Déconnexion manuelle');
                updateStatus('❌ Déconnecté', 'disconnected');
            }
        }
        
        function sendTestCommand() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('❌ Pas de connexion WebSocket active');
                return;
            }
            
            const testCommand = {
                action: 'frequency',
                data: { frequency: 145000000 },
                timestamp: Date.now()
            };
            
            log(`📤 Envoi commande: ${JSON.stringify(testCommand)}`);
            ws.send(JSON.stringify(testCommand));
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // Connexion automatique au chargement
        window.onload = function() {
            log('🚀 Page chargée - Connexion automatique...');
            connectWS();
        };
    </script>
</body>
</html>
