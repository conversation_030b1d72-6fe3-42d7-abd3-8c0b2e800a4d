#!/usr/bin/env python3
"""
Script de test pour la communication ICOM IC-R8600
Permet de vérifier la connexion et tester les commandes de base
"""

import sys
import time
from icom_handler import ICOMHandler

def test_connection():
    """Test de connexion basique"""
    print("=== Test de connexion ICOM IC-R8600 ===")
    
    # Créer l'instance (modifier le port selon votre configuration)
    handler = ICOMHandler(
        port="COM3",  # Modifier selon votre système
        baudrate=19200,
        use_udp=False
    )
    
    # Tenter la connexion
    print("Tentative de connexion...")
    if handler.connect():
        print("✅ Connexion établie")
    else:
        print("❌ Échec de connexion")
        return False
    
    return handler

def test_basic_commands(handler):
    """Test des commandes de base"""
    print("\n=== Test des commandes de base ===")
    
    # Test lecture fréquence
    print("Test lecture fréquence...")
    freq = handler.get_frequency()
    if freq:
        print(f"✅ Fréquence actuelle: {freq} Hz ({freq/1000000:.3f} MHz)")
    else:
        print("❌ Échec lecture fréquence")
    
    # Test lecture RSSI
    print("Test lecture RSSI...")
    rssi = handler.get_rssi()
    if rssi is not None:
        print(f"✅ RSSI: {rssi}")
    else:
        print("❌ Échec lecture RSSI")
    
    # Test changement de fréquence
    print("Test changement fréquence (145.500 MHz)...")
    if handler.set_frequency(145500000):
        print("✅ Fréquence définie")
        time.sleep(1)
        
        # Vérifier le changement
        new_freq = handler.get_frequency()
        if new_freq == 145500000:
            print("✅ Changement confirmé")
        else:
            print(f"⚠️  Fréquence différente: {new_freq}")
    else:
        print("❌ Échec changement fréquence")
    
    # Test changement de mode
    print("Test changement mode (FM)...")
    if handler.set_mode("FM"):
        print("✅ Mode FM défini")
    else:
        print("❌ Échec changement mode")
    
    # Test RF Gain
    print("Test RF Gain (128)...")
    if handler.set_rf_gain(128):
        print("✅ RF Gain défini")
    else:
        print("❌ Échec RF Gain")

def test_scan(handler):
    """Test du scan"""
    print("\n=== Test du scan ===")
    
    print("Démarrage scan 144-146 MHz...")
    if handler.start_scan(144000000, 146000000, 25000):
        print("✅ Scan démarré")
        
        print("Attente 5 secondes...")
        time.sleep(5)
        
        print("Arrêt du scan...")
        if handler.stop_scan():
            print("✅ Scan arrêté")
        else:
            print("❌ Échec arrêt scan")
    else:
        print("❌ Échec démarrage scan")

def test_status(handler):
    """Test de lecture d'état complet"""
    print("\n=== Test lecture état complet ===")
    
    status = handler.get_status()
    print("État du récepteur:")
    for key, value in status.items():
        print(f"  {key}: {value}")

def main():
    """Fonction principale"""
    print("Script de test ICOM IC-R8600")
    print("Assurez-vous que le récepteur est connecté et allumé")
    
    # Test de connexion
    handler = test_connection()
    if not handler:
        print("\n❌ Impossible de continuer sans connexion")
        sys.exit(1)
    
    try:
        # Tests des commandes
        test_basic_commands(handler)
        test_status(handler)
        test_scan(handler)
        
        print("\n=== Tests terminés ===")
        print("✅ Tous les tests sont terminés")
        
    except KeyboardInterrupt:
        print("\n⚠️  Tests interrompus par l'utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur pendant les tests: {e}")
    finally:
        # Fermer la connexion
        handler.disconnect()
        print("Connexion fermée")

if __name__ == "__main__":
    main()
