#!/bin/bash

echo "========================================"
echo "   ICOM IC-R8600 Controller"
echo "   Démarrage automatique"
echo "========================================"
echo

echo "[1/4] Installation des dépendances Python..."
cd backend
pip install -r ../requirements.txt
if [ $? -ne 0 ]; then
    echo "Erreur installation Python"
    exit 1
fi

echo
echo "[2/4] Installation des dépendances Node.js..."
cd ../frontend
npm install
if [ $? -ne 0 ]; then
    echo "Erreur installation Node.js"
    exit 1
fi

echo
echo "[3/4] Démarrage du backend FastAPI..."
cd ../backend
gnome-terminal --title="Backend FastAPI" -- bash -c "python main.py; exec bash" &

echo
echo "[4/4] Démarrage du frontend React..."
cd ../frontend
sleep 3
gnome-terminal --title="Frontend React" -- bash -c "npm run dev; exec bash" &

echo
echo "========================================"
echo "   Services démarrés !"
echo "   Backend:  http://localhost:8000"
echo "   Frontend: http://localhost:5173"
echo "   API Docs: http://localhost:8000/docs"
echo "========================================"
echo
echo "Appuyez sur Entrée pour continuer..."
read
