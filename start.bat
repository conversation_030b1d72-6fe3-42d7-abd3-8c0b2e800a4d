@echo off
echo ========================================
echo    ICOM IC-R8600 Controller
echo    Demarrage automatique
echo ========================================
echo.

echo [1/4] Installation des dependances Python...
cd backend
pip install -r ../requirements.txt
if %errorlevel% neq 0 (
    echo Erreur installation Python
    pause
    exit /b 1
)

echo.
echo [2/4] Installation des dependances Node.js...
cd ../frontend
call npm install
if %errorlevel% neq 0 (
    echo Erreur installation Node.js
    pause
    exit /b 1
)

echo.
echo [3/4] Demarrage du backend FastAPI...
cd ../backend
start "Backend FastAPI" cmd /k "python main.py"

echo.
echo [4/4] Demarrage du frontend React...
cd ../frontend
timeout /t 3 /nobreak > nul
start "Frontend React" cmd /k "npm run dev"

echo.
echo ========================================
echo    Services demarres !
echo    Backend:  http://localhost:8000
echo    Frontend: http://localhost:5173
echo    API Docs: http://localhost:8000/docs
echo ========================================
echo.
echo Appuyez sur une touche pour fermer...
pause > nul
