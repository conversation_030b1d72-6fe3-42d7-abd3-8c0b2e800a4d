#!/usr/bin/env python3
"""
Serveur simple pour servir l'interface React
"""

import http.server
import socketserver
import os
import webbrowser
import mimetypes
from urllib.parse import urlparse

# Ajouter les types MIME pour les fichiers JavaScript modernes
mimetypes.add_type('application/javascript', '.js')
mimetypes.add_type('application/javascript', '.jsx')
mimetypes.add_type('application/javascript', '.mjs')
mimetypes.add_type('text/css', '.css')

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # Ajouter les headers CORS
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()

    def guess_type(self, path):
        """Override pour forcer les bons types MIME"""
        try:
            # Appeler la méthode parent et gérer le retour
            parent_result = super().guess_type(path)
            
            # Gérer les différents types de retour selon la version de Python
            if isinstance(parent_result, tuple):
                if len(parent_result) >= 2:
                    mimetype = parent_result[0]
                    encoding = parent_result[1]
                else:
                    mimetype = parent_result[0] if parent_result else 'text/plain'
                    encoding = None
            else:
                mimetype = parent_result
                encoding = None

            # Forcer le type JavaScript pour les fichiers .jsx et .js
            if path.endswith(('.js', '.jsx', '.mjs')):
                mimetype = 'application/javascript'
            elif path.endswith('.css'):
                mimetype = 'text/css'

            # Retourner selon ce qui est attendu
            if encoding is not None:
                return mimetype, encoding
            else:
                return mimetype
                
        except Exception as e:
            # Fallback en cas d'erreur
            if path.endswith(('.js', '.jsx', '.mjs')):
                return 'application/javascript'
            elif path.endswith('.css'):
                return 'text/css'
            else:
                return 'text/plain'

    def do_GET(self):
        # Servir index.html pour toutes les routes (SPA)
        if self.path == '/' or not os.path.exists(self.path[1:]):
            self.path = '/index.html'
        return super().do_GET()

def main():
    PORT = 8080
    
    # Changer vers le répertoire frontend
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    print(f"🌐 Démarrage du serveur sur http://localhost:{PORT}")
    print("📁 Répertoire:", os.getcwd())
    
    with socketserver.TCPServer(("", PORT), CustomHTTPRequestHandler) as httpd:
        print(f"✅ Serveur démarré sur http://localhost:{PORT}")
        print("🔗 Ouvrez votre navigateur sur cette adresse")
        print("⏹️  Appuyez sur Ctrl+C pour arrêter")
        
        # Ouvrir automatiquement le navigateur
        try:
            webbrowser.open(f'http://localhost:{PORT}')
        except:
            pass
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 Serveur arrêté")

if __name__ == "__main__":
    main()
