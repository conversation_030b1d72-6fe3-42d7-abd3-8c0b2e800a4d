@echo off
echo.
echo ========================================
echo 🚀 ICOM IC-R8600 ULTRA-FAST CONTROLLER
echo ========================================
echo.

REM Configuration pour performance maximale
set PYTHONUNBUFFERED=1
set UVLOOP_ENABLED=1

echo 📡 Démarrage du serveur ultra-rapide...
echo.

REM Démarrer le serveur backend ultra-rapide
cd backend
start "ICOM Backend Ultra-Fast" cmd /k "python main_ultra_fast.py"

REM Attendre que le serveur démarre
timeout /t 3 /nobreak > nul

echo 🌐 Démarrage de l'interface web...
echo.

REM Démarrer le frontend
cd ..\frontend
start "ICOM Frontend" cmd /k "npm run dev"

echo.
echo ✅ Système ultra-rapide démarré !
echo.
echo 📊 Interfaces disponibles:
echo    - Backend API: http://localhost:8000
echo    - Frontend Web: http://localhost:5173
echo    - WebSocket: ws://localhost:8000/ws
echo.
echo 🎯 Fonctionnalités ultra-rapides:
echo    - Latence < 10ms pour les commandes
echo    - Mise à jour statut 20Hz (50ms)
echo    - Audio temps réel
echo    - Contrôles clavier instantanés
echo.
echo 🔧 Raccourcis clavier:
echo    - Flèches: Changement fréquence
echo    - Espace: Power ON/OFF
echo    - L: Verrouiller/Déverrouiller
echo    - M: Mute audio
echo    - R: Enregistrement
echo.
echo Appuyez sur une touche pour ouvrir l'interface...
pause > nul

REM Ouvrir l'interface web
start http://localhost:5173

echo.
echo 🚀 Interface ouverte ! Contrôle ultra-rapide activé.
echo.
pause
