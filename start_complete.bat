@echo off
echo.
echo ========================================
echo 🚀 ICOM IC-R8600 CONTROLLER - LANCEMENT COMPLET
echo ========================================
echo.

REM Arrêter les processus existants
echo 🛑 Arrêt des processus existants...
taskkill /f /im python.exe >nul 2>&1
taskkill /f /im node.exe >nul 2>&1

REM Attendre que les ports soient libérés
timeout /t 2 /nobreak >nul

echo 📡 Démarrage du backend ultra-rapide...
cd backend
start "ICOM Backend" cmd /k "python main_ultra_fast.py"

REM Attendre que le backend démarre
timeout /t 5 /nobreak >nul

echo 🌐 Démarrage du frontend...
cd ..\frontend
start "ICOM Frontend" cmd /k "npm run dev"

REM Attendre que le frontend démarre
timeout /t 8 /nobreak >nul

echo.
echo ✅ SYSTÈME DÉMARRÉ !
echo.
echo 📊 Interfaces disponibles:
echo    - Backend API: http://localhost:8000
echo    - Frontend Web: http://localhost:5173
echo    - WebSocket: ws://localhost:8000/ws
echo.
echo 🎯 Fonctionnalités:
echo    - Contrôle radio temps réel
echo    - Enregistrement audio
echo    - Scan automatique
echo    - Monitoring RSSI
echo.
echo 🔧 Raccourcis clavier:
echo    - Flèches: Changement fréquence
echo    - Espace: Power ON/OFF
echo    - L: Verrouiller/Déverrouiller
echo    - M: Mute audio
echo    - R: Enregistrement
echo.

REM Ouvrir l'interface web
start http://localhost:5173

echo 🚀 Interface ouverte ! Système opérationnel.
echo.
pause 