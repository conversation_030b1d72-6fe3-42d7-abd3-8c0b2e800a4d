#!/usr/bin/env python3
"""
Scanner réseau pour trouver l'IC-R8600 en DHCP
"""

import subprocess
import threading
import time
from concurrent.futures import ThreadPoolExecutor

def ping_host(ip):
    """Ping une adresse IP spécifique"""
    try:
        result = subprocess.run(
            ["ping", "-n", "1", "-w", "1000", ip],
            capture_output=True,
            text=True,
            timeout=3
        )
        
        if result.returncode == 0 and "TTL=" in result.stdout:
            return ip
    except:
        pass
    return None

def scan_network():
    """Scan du réseau 192.168.1.x pour trouver des hôtes actifs"""
    print("🔍 Scan du réseau 192.168.1.x...")
    print("Recherche de l'IC-R8600 en DHCP...")
    print()
    
    active_hosts = []
    
    # Utiliser ThreadPoolExecutor pour scanner en parallèle
    with ThreadPoolExecutor(max_workers=50) as executor:
        # Scanner les adresses de 1 à 254
        futures = []
        for i in range(1, 255):
            ip = f"192.168.1.{i}"
            future = executor.submit(ping_host, ip)
            futures.append(future)
        
        # Collecter les résultats
        for future in futures:
            result = future.result()
            if result:
                active_hosts.append(result)
                print(f"✅ Hôte actif trouvé: {result}")
    
    return active_hosts

def test_icom_ports(hosts):
    """Test les ports CI-V sur les hôtes trouvés"""
    print(f"\n🔍 Test des ports CI-V sur {len(hosts)} hôtes...")
    
    import socket
    
    for host in hosts:
        print(f"\n--- Test {host} ---")
        
        # Test port 50001 (CI-V standard)
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(2.0)
            
            # Commande CI-V simple
            cmd = bytes([0xFE, 0xFE, 0x96, 0xDF, 0x03, 0xFD])
            sock.sendto(cmd, (host, 50001))
            
            try:
                response, addr = sock.recvfrom(1024)
                print(f"🎉 ICOM IC-R8600 TROUVÉ sur {host}:50001 !")
                print(f"   Réponse: {' '.join([f'{b:02X}' for b in response])}")
                return host
            except socket.timeout:
                print(f"   Port 50001: Pas de réponse CI-V")
            
            sock.close()
            
        except Exception as e:
            print(f"   Erreur test {host}: {e}")
    
    return None

def main():
    """Fonction principale"""
    print("Scanner Réseau IC-R8600 DHCP")
    print("=============================")
    print("Ce script va:")
    print("1. Scanner le réseau 192.168.1.x")
    print("2. Tester les ports CI-V sur les hôtes trouvés")
    print("3. Identifier l'IC-R8600")
    print()
    
    # Scanner le réseau
    active_hosts = scan_network()
    
    if not active_hosts:
        print("❌ Aucun hôte actif trouvé sur le réseau 192.168.1.x")
        print("\nVérifiez que:")
        print("1. L'IC-R8600 est allumé")
        print("2. Le câble RJ45 est connecté")
        print("3. DHCP est activé sur l'IC-R8600")
        print("4. L'IC-R8600 a redémarré après la config DHCP")
        return
    
    print(f"\n📊 Résumé: {len(active_hosts)} hôtes actifs trouvés")
    for host in sorted(active_hosts, key=lambda x: int(x.split('.')[-1])):
        print(f"   - {host}")
    
    # Tester les ports CI-V
    icom_ip = test_icom_ports(active_hosts)
    
    if icom_ip:
        print(f"\n🎉 IC-R8600 trouvé sur: {icom_ip}")
        print(f"\nModifiez votre code backend:")
        print(f"udp_host=\"{icom_ip}\"")
        
        # Mettre à jour automatiquement le fichier main.py
        try:
            with open("main.py", "r", encoding="utf-8") as f:
                content = f.read()
            
            # Remplacer l'ancienne IP
            old_patterns = [
                'udp_host="*************"',
                'udp_host="***********"'
            ]
            
            for pattern in old_patterns:
                if pattern in content:
                    content = content.replace(pattern, f'udp_host="{icom_ip}"')
                    break
            
            with open("main.py", "w", encoding="utf-8") as f:
                f.write(content)
            
            print(f"✅ Fichier main.py mis à jour automatiquement")
            
        except Exception as e:
            print(f"⚠️  Erreur mise à jour automatique: {e}")
            print(f"   Modifiez manuellement main.py avec l'IP: {icom_ip}")
    
    else:
        print("\n❌ IC-R8600 non trouvé")
        print("\nHôtes actifs détectés (vérifiez manuellement):")
        for host in active_hosts:
            print(f"   - {host}")
        print("\nActions à vérifier:")
        print("1. Configuration CI-V sur l'IC-R8600")
        print("2. USB/LAN Remote: ON")
        print("3. CI-V Transceive: ON")

if __name__ == "__main__":
    main()
