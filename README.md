# ICOM IC-R8600 Controller

Un projet FullStack pour contrôler le récepteur ICOM IC-R8600 via CI-V.

## 🎯 Fonctionnalités

- **Contrôle distant** : ON/OFF, fréquence, modulation, RF gain, filtres
- **Monitoring** : Fréquence actuelle, RSSI en temps réel
- **Enregistrement audio** : Capture AF/IF en fichiers WAV
- **Scan programmable** : Balayage automatique de fréquences
- **Interface web moderne** : React + FastAPI

## 🏗️ Architecture

```
icom-controller/
├── frontend/           # React app (Vite)
│   ├── src/
│   │   ├── App.jsx
│   │   ├── components/
│   │   └── main.jsx
│   ├── package.json
│   └── index.html
├── backend/
│   ├── main.py         # FastAPI server
│   ├── icom_handler.py # CI-V communication
│   ├── audio_recorder.py # Audio capture
│   └── models.py       # Pydantic models
├── requirements.txt
└── README.md
```

## 🚀 Installation

### Backend
```bash
cd backend
pip install -r requirements.txt
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### Frontend
```bash
cd frontend
npm install
npm run dev
```

## 🔌 Configuration CI-V

- **Port série** : `/dev/ttyUSB0` (Linux) ou `COM3` (Windows)
- **Baudrate** : 19200
- **Adresse CI-V** : 0x96 (IC-R8600)
- **Adresse contrôleur** : 0xE0

## 📡 Commandes CI-V supportées

| Fonction | Commande | Exemple |
|----------|----------|---------|
| Power ON/OFF | `FE FE 96 E0 18 01 FD` | ON |
| Set Frequency | `FE FE 96 E0 05 [freq] FD` | 145.500 MHz |
| Get Frequency | `FE FE 96 E0 03 FD` | - |
| Set Mode | `FE FE 96 E0 06 [mode] FD` | AM/FM/USB |
| Get RSSI | `FE FE 96 E0 15 02 FD` | - |
| RF Gain | `FE FE 96 E0 14 02 [gain] FD` | 0-255 |

## 🎵 Enregistrement Audio

- **Format** : WAV 16-bit 48kHz
- **Sources** : AF (Audio Frequency) ou IF (Intermediate Frequency)
- **Stockage** : `backend/recordings/`

## 🌐 API Endpoints

- `POST /api/command` - Envoyer commande CI-V
- `GET /api/status` - État actuel du récepteur
- `POST /api/audio/start` - Démarrer enregistrement
- `POST /api/audio/stop` - Arrêter enregistrement
- `GET /api/recordings` - Liste des fichiers audio

## 📋 Utilisation

1. Connecter l'IC-R8600 via USB ou réseau
2. Lancer le backend FastAPI
3. Ouvrir l'interface React
4. Configurer la fréquence et modulation
5. Contrôler le récepteur en temps réel
