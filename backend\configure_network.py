#!/usr/bin/env python3
"""
Script de configuration réseau pour IC-R8600
"""

import subprocess
import socket
import time
import sys
import json

def check_admin_rights():
    """Vérifie si le script est exécuté en tant qu'administrateur"""
    try:
        import ctypes
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def get_network_interfaces():
    """Liste les interfaces réseau disponibles"""
    try:
        result = subprocess.run(
            ["netsh", "interface", "show", "interface"],
            capture_output=True,
            text=True
        )
        return result.stdout
    except Exception as e:
        print(f"Erreur lecture interfaces: {e}")
        return ""

def configure_static_ip():
    """Configure l'IP statique sur l'interface Ethernet"""
    if not check_admin_rights():
        print("❌ Ce script doit être exécuté en tant qu'administrateur")
        print("Clic droit → 'Exécuter en tant qu'administrateur'")
        return False
    
    try:
        # Configuration IP statique
        cmd = [
            "netsh", "interface", "ip", "set", "address",
            "name=Ethernet",
            "source=static",
            "addr=************",
            "mask=*************"
        ]
        
        print("Configuration de l'IP statique...")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ IP statique configurée : ************")
            
            # Configuration DNS
            dns_cmd = [
                "netsh", "interface", "ip", "set", "dns",
                "name=Ethernet",
                "source=static",
                "addr=*******"
            ]
            
            subprocess.run(dns_cmd, capture_output=True)
            print("✅ DNS configuré")
            
            return True
        else:
            print(f"❌ Erreur configuration: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_connectivity():
    """Test la connectivité avec l'IC-R8600"""
    print("\n🔍 Test de connectivité...")
    
    # Test ping
    try:
        result = subprocess.run(
            ["ping", "-n", "3", "*************"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            print("✅ Ping réussi vers *************")
            return True
        else:
            print("❌ Ping échoué")
            print("Vérifiez que l'IC-R8600 est configuré sur *************")
            return False
            
    except Exception as e:
        print(f"❌ Erreur ping: {e}")
        return False

def test_port_connection():
    """Test la connexion au port 50001"""
    print("🔍 Test du port 50001...")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5.0)
        
        result = sock.connect_ex(("*************", 50001))
        sock.close()
        
        if result == 0:
            print("✅ Port 50001 accessible")
            return True
        else:
            print("❌ Port 50001 fermé")
            print("Activez 'Remote Control' sur l'IC-R8600")
            return False
            
    except Exception as e:
        print(f"❌ Erreur test port: {e}")
        return False

def update_config_file():
    """Met à jour le fichier de configuration"""
    config_path = "config.json"
    
    try:
        # Charger la config existante
        try:
            with open(config_path, "r") as f:
                config = json.load(f)
        except:
            config = {}
        
        # Mettre à jour les paramètres réseau
        if "icom" not in config:
            config["icom"] = {}
        
        config["icom"]["connection_type"] = "network"
        config["icom"]["udp_host"] = "*************"
        config["icom"]["udp_port"] = 50001
        config["icom"]["fallback_to_serial"] = True
        
        # Sauvegarder
        with open(config_path, "w") as f:
            json.dump(config, f, indent=2)
        
        print("✅ Configuration mise à jour")
        return True
        
    except Exception as e:
        print(f"❌ Erreur mise à jour config: {e}")
        return False

def show_manual_config():
    """Affiche les instructions de configuration manuelle"""
    print("\n" + "="*60)
    print("CONFIGURATION MANUELLE REQUISE")
    print("="*60)
    
    print("\n🖥️ CONFIGURATION PC :")
    print("1. Panneau de configuration → Réseau et Internet")
    print("2. Modifier les paramètres de l'adaptateur")
    print("3. Clic droit sur 'Ethernet' → Propriétés")
    print("4. Double-clic sur 'Protocole Internet version 4'")
    print("5. Sélectionner 'Utiliser l'adresse IP suivante'")
    print("   - Adresse IP : ************")
    print("   - Masque : *************")
    print("   - Passerelle : (vide)")
    print("   - DNS : *******")
    
    print("\n📻 CONFIGURATION IC-R8600 :")
    print("1. MENU → SET → Connectors → LAN")
    print("   - IP Address : *************")
    print("   - Subnet Mask : *************")
    print("   - Gateway : (vide)")
    print("   - DHCP : OFF")
    print("\n2. MENU → SET → Connectors → LAN")
    print("   - Remote Control : ON")
    print("\n3. Redémarrer l'IC-R8600")

def main():
    """Fonction principale"""
    print("Configuration Réseau IC-R8600")
    print("==============================")
    
    # Afficher les interfaces actuelles
    print("\n📋 Interfaces réseau actuelles :")
    interfaces = get_network_interfaces()
    print(interfaces)
    
    # Vérifier les droits admin
    if check_admin_rights():
        print("✅ Droits administrateur détectés")
        
        # Proposer la configuration automatique
        response = input("\nConfigurer automatiquement l'IP statique ? (o/n): ")
        
        if response.lower() in ['o', 'oui', 'y', 'yes']:
            if configure_static_ip():
                print("✅ Configuration automatique réussie")
                
                # Attendre un peu pour que la config prenne effet
                print("⏳ Attente de la prise en compte...")
                time.sleep(3)
                
                # Tester la connectivité
                if test_connectivity():
                    if test_port_connection():
                        print("🎉 Configuration complète et fonctionnelle !")
                        update_config_file()
                    else:
                        print("⚠️ Configurez 'Remote Control' sur l'IC-R8600")
                else:
                    print("⚠️ Configurez l'IP de l'IC-R8600")
            else:
                show_manual_config()
        else:
            show_manual_config()
    else:
        print("⚠️ Pas de droits administrateur")
        show_manual_config()
    
    print("\n" + "="*60)
    print("Une fois configuré, testez avec :")
    print("python test_network.py")
    print("="*60)

if __name__ == "__main__":
    main()
