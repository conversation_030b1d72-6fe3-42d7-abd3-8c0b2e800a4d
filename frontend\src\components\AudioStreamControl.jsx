import React, { useState, useEffect, useRef, useCallback } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from './ui/card';
import { But<PERSON> } from './ui/button';
import { <PERSON>lider } from './ui/slider';
import { Badge } from './ui/badge';
import { 
  Volume2, 
  VolumeX, 
  Mic, 
  MicOff, 
  Radio,
  Activity,
  Headphones,
  Settings,
  Play,
  Pause,
  Square
} from 'lucide-react';

const AudioStreamControl = ({ radioStatus, onVolumeChange }) => {
  // États audio
  const [audioContext, setAudioContext] = useState(null);
  const [audioStream, setAudioStream] = useState(null);
  const [isStreaming, setIsStreaming] = useState(false);
  const [volume, setVolume] = useState(50);
  const [isMuted, setIsMuted] = useState(false);
  const [audioLevel, setAudioLevel] = useState(0);
  const [isRecording, setIsRecording] = useState(false);
  
  // Références
  const audioRef = useRef(null);
  const analyserRef = useRef(null);
  const gainNodeRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const animationFrameRef = useRef(null);

  // Initialisation du contexte audio
  const initAudioContext = useCallback(async () => {
    try {
      const context = new (window.AudioContext || window.webkitAudioContext)();
      setAudioContext(context);
      
      // Créer les nœuds audio
      const analyser = context.createAnalyser();
      analyser.fftSize = 256;
      analyser.smoothingTimeConstant = 0.8;
      analyserRef.current = analyser;
      
      const gainNode = context.createGain();
      gainNode.gain.value = volume / 100;
      gainNodeRef.current = gainNode;
      
      // Connecter à la sortie
      gainNode.connect(context.destination);
      analyser.connect(gainNode);
      
      console.log('🎵 Contexte audio initialisé');
      return context;
    } catch (error) {
      console.error('❌ Erreur initialisation audio:', error);
      return null;
    }
  }, [volume]);

  // Démarrage du streaming audio
  const startAudioStream = useCallback(async () => {
    try {
      let context = audioContext;
      if (!context) {
        context = await initAudioContext();
        if (!context) return;
      }

      // Simuler le stream audio du récepteur
      // En production, ceci serait connecté à l'audio de l'IC-R8600
      const oscillator = context.createOscillator();
      const noiseBuffer = context.createBuffer(1, context.sampleRate * 2, context.sampleRate);
      const noiseData = noiseBuffer.getChannelData(0);
      
      // Générer du bruit blanc (simulation RF)
      for (let i = 0; i < noiseData.length; i++) {
        noiseData[i] = (Math.random() * 2 - 1) * 0.1;
      }
      
      const noiseSource = context.createBufferSource();
      noiseSource.buffer = noiseBuffer;
      noiseSource.loop = true;
      
      // Filtrage pour simuler la réception
      const filter = context.createBiquadFilter();
      filter.type = 'bandpass';
      filter.frequency.value = 1000 + (radioStatus.frequency % 10000) / 10;
      filter.Q.value = 10;
      
      // Connexion du pipeline audio
      noiseSource.connect(filter);
      filter.connect(analyserRef.current);
      
      noiseSource.start();
      setAudioStream(noiseSource);
      setIsStreaming(true);
      
      // Démarrer l'analyse du niveau audio
      startAudioAnalysis();
      
      console.log('🎵 Streaming audio démarré');
    } catch (error) {
      console.error('❌ Erreur démarrage stream:', error);
    }
  }, [audioContext, initAudioContext, radioStatus.frequency]);

  // Arrêt du streaming
  const stopAudioStream = useCallback(() => {
    if (audioStream) {
      audioStream.stop();
      setAudioStream(null);
    }
    setIsStreaming(false);
    
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }
    
    console.log('🔇 Streaming audio arrêté');
  }, [audioStream]);

  // Analyse du niveau audio
  const startAudioAnalysis = useCallback(() => {
    if (!analyserRef.current) return;
    
    const analyser = analyserRef.current;
    const dataArray = new Uint8Array(analyser.frequencyBinCount);
    
    const analyze = () => {
      analyser.getByteFrequencyData(dataArray);
      
      // Calculer le niveau moyen
      let sum = 0;
      for (let i = 0; i < dataArray.length; i++) {
        sum += dataArray[i];
      }
      const average = sum / dataArray.length;
      setAudioLevel(Math.round((average / 255) * 100));
      
      animationFrameRef.current = requestAnimationFrame(analyze);
    };
    
    analyze();
  }, []);

  // Contrôle du volume
  const handleVolumeChange = useCallback((newVolume) => {
    setVolume(newVolume);
    if (gainNodeRef.current) {
      gainNodeRef.current.gain.value = isMuted ? 0 : newVolume / 100;
    }
    if (onVolumeChange) {
      onVolumeChange(newVolume);
    }
  }, [isMuted, onVolumeChange]);

  // Contrôle mute
  const toggleMute = useCallback(() => {
    const newMuted = !isMuted;
    setIsMuted(newMuted);
    if (gainNodeRef.current) {
      gainNodeRef.current.gain.value = newMuted ? 0 : volume / 100;
    }
  }, [isMuted, volume]);

  // Démarrage/arrêt enregistrement
  const toggleRecording = useCallback(async () => {
    if (!isRecording) {
      try {
        // Créer un stream pour l'enregistrement
        const stream = audioContext.createMediaStreamDestination();
        if (analyserRef.current) {
          analyserRef.current.connect(stream);
        }
        
        const mediaRecorder = new MediaRecorder(stream.stream);
        const chunks = [];
        
        mediaRecorder.ondataavailable = (event) => {
          chunks.push(event.data);
        };
        
        mediaRecorder.onstop = () => {
          const blob = new Blob(chunks, { type: 'audio/wav' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `recording_${Date.now()}.wav`;
          a.click();
          URL.revokeObjectURL(url);
        };
        
        mediaRecorder.start();
        mediaRecorderRef.current = mediaRecorder;
        setIsRecording(true);
        
        console.log('🎙️ Enregistrement démarré');
      } catch (error) {
        console.error('❌ Erreur enregistrement:', error);
      }
    } else {
      if (mediaRecorderRef.current) {
        mediaRecorderRef.current.stop();
        mediaRecorderRef.current = null;
      }
      setIsRecording(false);
      console.log('⏹️ Enregistrement arrêté');
    }
  }, [isRecording, audioContext]);

  // Nettoyage
  useEffect(() => {
    return () => {
      stopAudioStream();
      if (audioContext) {
        audioContext.close();
      }
    };
  }, [stopAudioStream, audioContext]);

  return (
    <Card className="bg-gray-800 border-gray-700">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Headphones className="w-5 h-5 text-purple-400" />
          <span>AUDIO TEMPS RÉEL</span>
          <Badge variant={isStreaming ? "default" : "secondary"}>
            {isStreaming ? "STREAMING" : "ARRÊTÉ"}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Contrôles principaux */}
        <div className="flex items-center justify-between">
          <Button
            variant={isStreaming ? "destructive" : "default"}
            onClick={isStreaming ? stopAudioStream : startAudioStream}
            className="w-32"
          >
            {isStreaming ? (
              <>
                <Square className="w-4 h-4 mr-2" />
                STOP
              </>
            ) : (
              <>
                <Play className="w-4 h-4 mr-2" />
                START
              </>
            )}
          </Button>
          
          <Button
            variant={isRecording ? "destructive" : "outline"}
            onClick={toggleRecording}
            disabled={!isStreaming}
          >
            {isRecording ? (
              <>
                <Square className="w-4 h-4 mr-2" />
                REC
              </>
            ) : (
              <>
                <Mic className="w-4 h-4 mr-2" />
                REC
              </>
            )}
          </Button>
        </div>

        {/* Niveau audio */}
        <div className="space-y-2">
          <div className="flex justify-between">
            <span className="text-sm text-gray-400">NIVEAU AUDIO</span>
            <span className="text-sm font-mono text-green-400">{audioLevel}%</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div 
              className="bg-gradient-to-r from-green-500 to-yellow-500 h-2 rounded-full transition-all duration-100"
              style={{ width: `${audioLevel}%` }}
            />
          </div>
        </div>

        {/* Contrôle volume */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-400">VOLUME</span>
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleMute}
              >
                {isMuted ? (
                  <VolumeX className="w-4 h-4 text-red-400" />
                ) : (
                  <Volume2 className="w-4 h-4 text-blue-400" />
                )}
              </Button>
              <span className="text-sm font-mono text-blue-400 w-12">
                {isMuted ? "MUTE" : `${volume}%`}
              </span>
            </div>
          </div>
          <Slider
            value={[volume]}
            onValueChange={([value]) => handleVolumeChange(value)}
            max={100}
            step={1}
            className="w-full"
            disabled={isMuted}
          />
        </div>

        {/* Informations audio */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="space-y-1">
            <div className="text-gray-400">Format:</div>
            <div className="font-mono text-green-400">48kHz/16bit</div>
          </div>
          <div className="space-y-1">
            <div className="text-gray-400">Latence:</div>
            <div className="font-mono text-green-400">
              {audioContext ? `${Math.round(audioContext.baseLatency * 1000)}ms` : 'N/A'}
            </div>
          </div>
        </div>

        {/* Visualiseur spectral simple */}
        {isStreaming && (
          <div className="space-y-2">
            <div className="text-sm text-gray-400">SPECTRE AUDIO</div>
            <div className="flex items-end space-x-1 h-16 bg-gray-900 rounded p-2">
              {Array.from({ length: 20 }, (_, i) => (
                <div
                  key={i}
                  className="bg-gradient-to-t from-blue-500 to-purple-500 w-2 rounded-t transition-all duration-100"
                  style={{ 
                    height: `${Math.random() * 100}%`,
                    opacity: isStreaming ? 0.8 : 0.3
                  }}
                />
              ))}
            </div>
          </div>
        )}

        {/* Raccourcis */}
        <div className="p-3 bg-gray-700 rounded text-xs">
          <div className="text-gray-400 mb-1">RACCOURCIS AUDIO:</div>
          <div className="space-y-1">
            <div>M: Mute/Unmute</div>
            <div>R: Démarrer/Arrêter enregistrement</div>
            <div>+/-: Volume ±10%</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default AudioStreamControl;
