#!/usr/bin/env python3
"""
Test complet COM6 avec toutes les commandes
"""

import serial
import time

def decode_frequency(response):
    """Décode la fréquence depuis la réponse CI-V"""
    if len(response) >= 11:
        # Les 5 bytes de fréquence sont aux positions 6-10
        freq_bytes = response[6:11]
        freq_str = ""
        
        # Décodage BCD (ordre inversé)
        for byte in reversed(freq_bytes):
            digit1 = (byte >> 4) & 0x0F
            digit2 = byte & 0x0F
            freq_str += f"{digit1}{digit2}"
        
        try:
            return int(freq_str)
        except:
            return None
    return None

def test_com6_complete():
    """Test complet de COM6"""
    print("=== Test Complet COM6 ===")
    
    try:
        ser = serial.Serial('COM6', 19200, timeout=2.0)
        print("✅ COM6 connecté")
        
        # Test 1: Lecture fréquence
        print("\n--- Test 1: Lecture Fréquence ---")
        cmd = bytes([0xFE, 0xFE, 0x96, 0x00, 0x03, 0xFD])
        ser.write(cmd)
        time.sleep(0.3)
        
        response = ser.read(50)
        if response:
            print(f"Réponse: {' '.join([f'{b:02X}' for b in response])}")
            freq = decode_frequency(response)
            if freq:
                print(f"🎯 Fréquence actuelle: {freq:,} Hz ({freq/1000000:.3f} MHz)")
        
        # Test 2: Changement de fréquence (145.500 MHz)
        print("\n--- Test 2: Changement Fréquence (145.500 MHz) ---")
        # 145500000 Hz en BCD: 00 00 50 45 01
        freq_bcd = [0x00, 0x00, 0x50, 0x45, 0x01]
        cmd = bytes([0xFE, 0xFE, 0x96, 0x00, 0x05] + freq_bcd + [0xFD])
        print(f"Envoi: {' '.join([f'{b:02X}' for b in cmd])}")
        
        ser.write(cmd)
        time.sleep(0.5)
        
        response = ser.read(50)
        if response:
            print(f"Réponse: {' '.join([f'{b:02X}' for b in response])}")
            if b'\xFE\xFE\x00\x96\xFB\xFD' in response:
                print("✅ Commande acceptée")
            else:
                print("⚠️ Réponse inattendue")
        
        # Vérification du changement
        time.sleep(0.5)
        cmd = bytes([0xFE, 0xFE, 0x96, 0x00, 0x03, 0xFD])
        ser.write(cmd)
        time.sleep(0.3)
        
        response = ser.read(50)
        if response:
            freq = decode_frequency(response)
            if freq:
                print(f"🎯 Nouvelle fréquence: {freq:,} Hz ({freq/1000000:.3f} MHz)")
        
        # Test 3: Lecture mode
        print("\n--- Test 3: Lecture Mode ---")
        cmd = bytes([0xFE, 0xFE, 0x96, 0x00, 0x04, 0xFD])
        ser.write(cmd)
        time.sleep(0.3)
        
        response = ser.read(50)
        if response:
            print(f"Réponse: {' '.join([f'{b:02X}' for b in response])}")
            if len(response) >= 7:
                mode_byte = response[6]
                modes = {0x00: "LSB", 0x01: "USB", 0x02: "AM", 0x03: "CW", 
                        0x05: "FM", 0x06: "WFM", 0x07: "CWR", 0x08: "RTTY"}
                mode_name = modes.get(mode_byte, f"Mode {mode_byte:02X}")
                print(f"🎯 Mode actuel: {mode_name}")
        
        # Test 4: Changement mode FM
        print("\n--- Test 4: Changement Mode FM ---")
        cmd = bytes([0xFE, 0xFE, 0x96, 0x00, 0x06, 0x05, 0xFD])  # 0x05 = FM
        ser.write(cmd)
        time.sleep(0.3)
        
        response = ser.read(50)
        if response:
            print(f"Réponse: {' '.join([f'{b:02X}' for b in response])}")
        
        # Test 5: Power status
        print("\n--- Test 5: Power Status ---")
        cmd = bytes([0xFE, 0xFE, 0x96, 0x00, 0x18, 0x00, 0xFD])
        ser.write(cmd)
        time.sleep(0.3)
        
        response = ser.read(50)
        if response:
            print(f"Réponse: {' '.join([f'{b:02X}' for b in response])}")
            if len(response) >= 7:
                power_byte = response[6]
                power_status = "ON" if power_byte == 0x01 else "OFF"
                print(f"🎯 Alimentation: {power_status}")
        
        # Test 6: RSSI
        print("\n--- Test 6: RSSI ---")
        cmd = bytes([0xFE, 0xFE, 0x96, 0x00, 0x15, 0x02, 0xFD])
        ser.write(cmd)
        time.sleep(0.3)
        
        response = ser.read(50)
        if response:
            print(f"Réponse: {' '.join([f'{b:02X}' for b in response])}")
            if len(response) >= 8:
                rssi_bytes = response[6:8]
                rssi = (rssi_bytes[0] << 8) | rssi_bytes[1] if len(rssi_bytes) >= 2 else rssi_bytes[0]
                print(f"🎯 RSSI: {rssi}")
        
        ser.close()
        print("\n🎉 Test complet terminé avec succès !")
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

if __name__ == "__main__":
    test_com6_complete()
