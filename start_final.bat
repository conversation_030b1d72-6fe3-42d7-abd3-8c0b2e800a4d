@echo off
echo.
echo ========================================
echo 🚀 ICOM IC-R8600 CONTROLLER - VERSION FINALE
echo ========================================
echo.

REM Arrêter les processus existants
echo 🛑 Arrêt des processus existants...
taskkill /f /im python.exe >nul 2>&1
taskkill /f /im node.exe >nul 2>&1

REM Attendre que les ports soient libérés
timeout /t 3 /nobreak >nul

echo 📡 Démarrage du backend ultra-rapide...
cd backend
start "ICOM Backend Ultra-Fast" cmd /k "python main_ultra_fast.py"

REM Attendre que le backend démarre
timeout /t 6 /nobreak >nul

echo 🌐 Démarrage du frontend Vite...
cd ..\frontend
start "ICOM Frontend Vite" cmd /k "npm run dev"

REM Attendre que le frontend démarre
timeout /t 10 /nobreak >nul

echo.
echo ✅ SYSTÈME DÉMARRÉ AVEC SUCCÈS !
echo.
echo 📊 Interfaces disponibles:
echo    - Backend API: http://localhost:8000
echo    - Frontend Web: http://localhost:5173
echo    - API Docs: http://localhost:8000/docs
echo    - WebSocket: ws://localhost:8000/ws
echo.
echo 🎯 Fonctionnalités opérationnelles:
echo    ✅ Contrôle radio temps réel
echo    ✅ Changement fréquence ultra-rapide
echo    ✅ Contrôle mode (FM/AM/USB/LSB)
echo    ✅ RF Gain ajustable
echo    ✅ Power ON/OFF
echo    ✅ Monitoring RSSI
echo    ✅ Interface web moderne
echo.
echo 🔧 Raccourcis clavier:
echo    - Flèches: Changement fréquence
echo    - Espace: Power ON/OFF
echo    - L: Verrouiller/Déverrouiller
echo    - M: Mute audio
echo    - R: Enregistrement
echo.

REM Ouvrir l'interface web
start http://localhost:5173

echo 🚀 Interface ouverte ! Système opérationnel.
echo.
echo 💡 Note: Si l'ICOM n'est pas connecté physiquement,
echo    le système fonctionne en mode simulation.
echo.
pause 