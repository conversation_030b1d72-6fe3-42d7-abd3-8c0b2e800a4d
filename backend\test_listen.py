#!/usr/bin/env python3
"""
Écoute passive pour détecter les réponses de l'IC-R8600
"""

import socket
import threading
import time

def listen_on_port(port):
    """Écoute sur un port spécifique"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.bind(('', port))
        sock.settimeout(1.0)
        
        print(f"🎧 Écoute sur port {port}...")
        
        start_time = time.time()
        while time.time() - start_time < 30:  # Écouter 30 secondes
            try:
                data, addr = sock.recvfrom(1024)
                print(f"📡 RÉCEPTION sur port {port} de {addr}")
                print(f"   Données: {' '.join([f'{b:02X}' for b in data])}")
                print(f"   Longueur: {len(data)} bytes")
                print()
                
            except socket.timeout:
                continue
            except Exception as e:
                print(f"❌ Erreur port {port}: {e}")
                break
        
        sock.close()
        
    except Exception as e:
        print(f"❌ Impossible d'écouter sur port {port}: {e}")

def send_commands_continuously():
    """Envoie des commandes en continu sur différents ports"""
    ports = [50001, 50002, 4532, 4533]
    
    for i in range(10):  # 10 tentatives
        for port in ports:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                
                # Commande lecture fréquence
                cmd = bytes([0xFE, 0xFE, 0x96, 0xDF, 0x03, 0xFD])
                sock.sendto(cmd, ("***********", port))
                
                print(f"📤 Envoi #{i+1} sur port {port}")
                sock.close()
                
            except Exception as e:
                print(f"❌ Erreur envoi port {port}: {e}")
        
        time.sleep(3)  # Attendre 3 secondes entre les envois

def main():
    """Fonction principale"""
    print("=== Écoute Passive IC-R8600 ===")
    print("Démarrage de l'écoute sur plusieurs ports...")
    print("Envoi de commandes en parallèle...")
    print()
    
    # Ports à écouter
    listen_ports = [50001, 50002, 4532, 4533, 50000, 50003]
    
    # Démarrer les threads d'écoute
    threads = []
    for port in listen_ports:
        thread = threading.Thread(target=listen_on_port, args=(port,))
        thread.daemon = True
        thread.start()
        threads.append(thread)
    
    # Attendre un peu que les listeners démarrent
    time.sleep(2)
    
    # Démarrer l'envoi de commandes
    send_thread = threading.Thread(target=send_commands_continuously)
    send_thread.daemon = True
    send_thread.start()
    
    print("⏳ Écoute en cours pendant 30 secondes...")
    print("   Si l'IC-R8600 répond, vous verrez les données ici.")
    print()
    
    # Attendre la fin
    time.sleep(32)
    
    print("🏁 Fin de l'écoute")
    print()
    print("Si aucune réponse n'a été détectée:")
    print("1. Vérifiez que l'IC-R8600 est en mode REMOTE")
    print("2. Vérifiez la configuration CI-V over LAN")
    print("3. Redémarrez l'IC-R8600")
    print("4. Essayez un logiciel ICOM officiel pour tester")

if __name__ == "__main__":
    main()
