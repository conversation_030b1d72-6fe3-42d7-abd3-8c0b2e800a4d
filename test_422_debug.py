#!/usr/bin/env python3
"""
Debug erreur 422 - Test des différents types de commandes
"""

import requests
import json

def test_command(data, description):
    """Test une commande spécifique"""
    print(f"\n=== {description} ===")
    print(f"Données envoyées: {json.dumps(data, indent=2)}")
    
    try:
        response = requests.post(
            "http://localhost:8000/api/command",
            json=data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 422:
            print("❌ Erreur 422 - Données invalides")
            try:
                error_detail = response.json()
                print(f"Détail erreur: {json.dumps(error_detail, indent=2)}")
            except:
                print(f"Réponse brute: {response.text}")
        elif response.status_code == 200:
            print("✅ Succès")
            result = response.json()
            print(f"Résultat: {result}")
        else:
            print(f"⚠️ Code inattendu: {response.status_code}")
            print(f"Réponse: {response.text}")
            
    except Exception as e:
        print(f"❌ Erreur réseau: {e}")

def main():
    print("Debug Erreur 422 - Test des Commandes")
    print("====================================")
    
    # Test 1: Power ON (fonctionne)
    test_command({"power_on": True}, "Power ON")
    
    # Test 2: Power OFF (fonctionne)
    test_command({"power_on": False}, "Power OFF")
    
    # Test 3: Fréquence seule
    test_command({"frequency": 145500000}, "Fréquence seule")
    
    # Test 4: Mode seul
    test_command({"mode": "FM"}, "Mode seul")
    
    # Test 5: RF Gain seul
    test_command({"rf_gain": 128}, "RF Gain seul")
    
    # Test 6: Fréquence + Mode
    test_command({
        "frequency": 145500000,
        "mode": "FM"
    }, "Fréquence + Mode")
    
    # Test 7: Tous les paramètres
    test_command({
        "frequency": 145500000,
        "mode": "FM",
        "rf_gain": 128,
        "power_on": True
    }, "Tous les paramètres")
    
    # Test 8: Fréquence invalide
    test_command({"frequency": "invalid"}, "Fréquence invalide")
    
    # Test 9: Mode invalide
    test_command({"mode": "INVALID_MODE"}, "Mode invalide")
    
    # Test 10: RF Gain invalide
    test_command({"rf_gain": 999}, "RF Gain invalide")

if __name__ == "__main__":
    main()
