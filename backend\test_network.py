#!/usr/bin/env python3
"""
Test de communication réseau avec IC-R8600 via RJ45
"""

import socket
import time
import struct
import logging
from network_handler import ICOMNetworkHandler, NetworkConfig

def test_basic_connection():
    """Test de connexion TCP basique"""
    print("=== Test Connexion TCP Basique ===")
    
    host = "*************"
    port = 50001
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5.0)
        
        print(f"Tentative de connexion à {host}:{port}")
        result = sock.connect_ex((host, port))
        
        if result == 0:
            print("✅ Connexion TCP réussie")
            
            # Test d'envoi de données
            test_data = b"HELLO"
            sock.send(test_data)
            
            # Tentative de lecture
            try:
                response = sock.recv(1024)
                if response:
                    print(f"✅ Réponse reçue: {response.hex()}")
                else:
                    print("⚠️ Pas de réponse")
            except socket.timeout:
                print("⚠️ Timeout sur la réponse")
            
            sock.close()
            return True
        else:
            print(f"❌ Connexion échouée: {result}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_udp_connection():
    """Test de connexion UDP"""
    print("\n=== Test Connexion UDP ===")
    
    host = "*************"
    port = 50001
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.settimeout(3.0)
        
        # Test CI-V via UDP
        ci_v_command = bytes([0xFE, 0xFE, 0x96, 0xE0, 0x03, 0xFD])
        print(f"Envoi commande CI-V: {ci_v_command.hex()}")
        
        sock.sendto(ci_v_command, (host, port))
        
        try:
            response, addr = sock.recvfrom(1024)
            print(f"✅ Réponse UDP: {response.hex()} de {addr}")
            return True
        except socket.timeout:
            print("⚠️ Timeout UDP")
            return False
            
    except Exception as e:
        print(f"❌ Erreur UDP: {e}")
        return False

def test_network_handler():
    """Test du handler réseau personnalisé"""
    print("\n=== Test Network Handler ===")
    
    config = NetworkConfig(
        host="*************",
        port=50001,
        timeout=5.0
    )
    
    handler = ICOMNetworkHandler(config)
    
    if handler.connect():
        print("✅ Handler connecté")
        
        # Test des commandes
        print("Test set_frequency...")
        if handler.set_frequency(145500000):
            print("✅ Fréquence définie")
        else:
            print("❌ Erreur fréquence")
        
        print("Test set_mode...")
        if handler.set_mode("FM"):
            print("✅ Mode défini")
        else:
            print("❌ Erreur mode")
        
        print("Test get_status...")
        status = handler.get_status()
        print(f"Status: {status}")
        
        handler.disconnect()
        return True
    else:
        print("❌ Connexion handler échouée")
        return False

def scan_ports():
    """Scan des ports ouverts sur l'IC-R8600"""
    print("\n=== Scan des Ports ===")
    
    host = "*************"
    common_ports = [50001, 50002, 8080, 8000, 23, 80, 443, 22, 21]
    
    open_ports = []
    
    for port in common_ports:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1.0)
            
            result = sock.connect_ex((host, port))
            if result == 0:
                print(f"✅ Port {port} ouvert")
                open_ports.append(port)
            else:
                print(f"❌ Port {port} fermé")
            
            sock.close()
            
        except Exception as e:
            print(f"⚠️ Erreur port {port}: {e}")
    
    return open_ports

def test_ping():
    """Test de ping réseau"""
    print("\n=== Test Ping ===")
    
    import subprocess
    import sys
    
    host = "*************"
    
    try:
        if sys.platform.startswith('win'):
            result = subprocess.run(
                ["ping", "-n", "3", host],
                capture_output=True,
                text=True,
                timeout=10
            )
        else:
            result = subprocess.run(
                ["ping", "-c", "3", host],
                capture_output=True,
                text=True,
                timeout=10
            )
        
        if result.returncode == 0:
            print("✅ Ping réussi")
            print(result.stdout)
            return True
        else:
            print("❌ Ping échoué")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Erreur ping: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("Test Communication Réseau IC-R8600")
    print("==================================")
    
    # Configuration logging
    logging.basicConfig(level=logging.INFO)
    
    results = {}
    
    # Tests séquentiels
    results['ping'] = test_ping()
    results['port_scan'] = len(scan_ports()) > 0
    results['tcp_connection'] = test_basic_connection()
    results['udp_connection'] = test_udp_connection()
    results['network_handler'] = test_network_handler()
    
    # Résumé
    print("\n" + "="*50)
    print("RÉSUMÉ DES TESTS")
    print("="*50)
    
    for test_name, success in results.items():
        status = "✅ RÉUSSI" if success else "❌ ÉCHOUÉ"
        print(f"{test_name:20} : {status}")
    
    # Recommandations
    print("\n" + "="*50)
    print("RECOMMANDATIONS")
    print("="*50)
    
    if results['ping']:
        print("✅ Connectivité réseau OK")
    else:
        print("❌ Vérifiez la configuration IP et les câbles")
    
    if results['tcp_connection']:
        print("✅ Port TCP accessible")
    else:
        print("❌ Vérifiez que le contrôle réseau est activé sur l'IC-R8600")
    
    if not any(results.values()):
        print("\n🔧 ACTIONS À EFFECTUER :")
        print("1. Vérifiez l'adresse IP de l'IC-R8600")
        print("2. Activez 'Remote Control' dans les menus")
        print("3. Vérifiez la configuration réseau")
        print("4. Testez avec le logiciel RS-R8600 officiel")

if __name__ == "__main__":
    main()
