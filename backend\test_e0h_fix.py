#!/usr/bin/env python3
"""
Test avec adresse E0h pour corriger le problème FA
"""

import serial
import time

def test_e0h_commands():
    """Test avec adresse E0h"""
    print("=== Test Correction Adresse E0h ===")
    
    try:
        ser = serial.Serial('COM6', 19200, timeout=2.0)
        print("✅ COM6 connecté")
        
        # Test 1: Lecture fréquence avec E0h
        print("\n--- Test 1: Lecture Fréquence (E0h) ---")
        cmd = bytes([0xFE, 0xFE, 0x96, 0xE0, 0x03, 0xFD])
        print(f"Envoi: {' '.join([f'{b:02X}' for b in cmd])}")
        
        ser.write(cmd)
        time.sleep(0.5)
        
        response = ser.read(50)
        if response:
            print(f"Réponse: {' '.join([f'{b:02X}' for b in response])}")
            if b'\xFB' in response:
                print("✅ FB (OK) reçu")
            elif b'\xFA' in response:
                print("❌ FA (ERREUR) reçu")
        
        # Test 2: Changement fréquence avec E0h
        print("\n--- Test 2: Changement Fréquence (E0h) ---")
        # 145500000 Hz en BCD: 00 00 50 45 01
        freq_bcd = [0x00, 0x00, 0x50, 0x45, 0x01]
        cmd = bytes([0xFE, 0xFE, 0x96, 0xE0, 0x05] + freq_bcd + [0xFD])
        print(f"Envoi: {' '.join([f'{b:02X}' for b in cmd])}")
        
        ser.write(cmd)
        time.sleep(0.5)
        
        response = ser.read(50)
        if response:
            print(f"Réponse: {' '.join([f'{b:02X}' for b in response])}")
            if b'\xFB' in response:
                print("✅ FB (OK) reçu - Commande acceptée")
            elif b'\xFA' in response:
                print("❌ FA (ERREUR) reçu - Commande rejetée")
        
        # Test 3: Test avec DFh
        print("\n--- Test 3: Lecture Fréquence (DFh) ---")
        cmd = bytes([0xFE, 0xFE, 0x96, 0xDF, 0x03, 0xFD])
        print(f"Envoi: {' '.join([f'{b:02X}' for b in cmd])}")
        
        ser.write(cmd)
        time.sleep(0.5)
        
        response = ser.read(50)
        if response:
            print(f"Réponse: {' '.join([f'{b:02X}' for b in response])}")
            if b'\xFB' in response:
                print("✅ FB (OK) reçu")
            elif b'\xFA' in response:
                print("❌ FA (ERREUR) reçu")
        
        # Test 4: Test sans adresse (broadcast pur)
        print("\n--- Test 4: Broadcast Pur ---")
        cmd = bytes([0xFE, 0xFE, 0x00, 0x96, 0x03, 0xFD])
        print(f"Envoi: {' '.join([f'{b:02X}' for b in cmd])}")
        
        ser.write(cmd)
        time.sleep(0.5)
        
        response = ser.read(50)
        if response:
            print(f"Réponse: {' '.join([f'{b:02X}' for b in response])}")
        
        ser.close()
        
    except Exception as e:
        print(f"❌ Erreur: {e}")

def test_configuration_check():
    """Vérification de la configuration IC-R8600"""
    print("\n=== Vérification Configuration ===")
    
    try:
        ser = serial.Serial('COM6', 19200, timeout=2.0)
        
        # Lecture de l'adresse CI-V configurée
        print("Test lecture configuration CI-V...")
        
        # Commande pour lire l'adresse transceive
        cmd = bytes([0xFE, 0xFE, 0x96, 0xE0, 0x1A, 0x05, 0x01, 0x12, 0xFD])
        ser.write(cmd)
        time.sleep(0.5)
        
        response = ser.read(50)
        if response:
            print(f"Config CI-V: {' '.join([f'{b:02X}' for b in response])}")
        
        ser.close()
        
    except Exception as e:
        print(f"❌ Erreur config: {e}")

if __name__ == "__main__":
    test_e0h_commands()
    test_configuration_check()
    
    print("\n" + "="*50)
    print("DIAGNOSTIC")
    print("="*50)
    print("Si toutes les réponses sont FA (ERREUR):")
    print("1. L'IC-R8600 est en mode REMOTE ✅")
    print("2. Mais l'adresse contrôleur est incorrecte ❌")
    print("3. Changez 'Transceive Address' sur l'IC-R8600")
    print("4. Essayez E0h ou DFh au lieu de 00h")
    print("\nSi FB (OK) apparaît:")
    print("1. L'adresse est correcte ✅")
    print("2. Le problème est dans le code backend ❌")
