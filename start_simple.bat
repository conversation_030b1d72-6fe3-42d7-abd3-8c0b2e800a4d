@echo off
echo.
echo ========================================
echo 🚀 ICOM IC-R8600 - DÉMARRAGE SIMPLE
echo ========================================
echo.

REM Arrêter les processus existants
echo 🛑 Arrêt des processus existants...
taskkill /f /im python.exe >nul 2>&1
taskkill /f /im node.exe >nul 2>&1

REM Attendre
timeout /t 3 /nobreak >nul

echo 📡 Démarrage du backend...
cd backend
start "ICOM Backend" cmd /k "python main_ultra_fast.py"

echo 🌐 Démarrage du frontend...
cd ..\frontend
start "ICOM Frontend" cmd /k "npm run dev"

echo.
echo ✅ Services démarrés !
echo.
echo 📊 Interfaces:
echo    - Backend: http://localhost:8000
echo    - Frontend: http://localhost:5173
echo.
echo 🔗 Ouverture de l'interface...
timeout /t 8 /nobreak >nul
start http://localhost:5173

echo 🚀 Projet lancé !
pause 