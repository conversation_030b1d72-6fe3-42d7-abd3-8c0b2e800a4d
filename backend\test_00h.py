#!/usr/bin/env python3
"""
Test avec adresse 00h (broadcast)
"""

import serial
import time

def test_00h():
    """Test avec adresse 00h"""
    print("=== Test avec Adresse 00h (Broadcast) ===")
    
    try:
        ser = serial.Serial('COM3', 19200, timeout=2.0)
        print("✅ Port COM3 ouvert")
        
        # Test avec 00h (broadcast)
        cmd = bytes([0xFE, 0xFE, 0x96, 0x00, 0x03, 0xFD])
        print(f"Envoi 00h: {' '.join([f'{b:02X}' for b in cmd])}")
        
        ser.write(cmd)
        time.sleep(0.5)
        
        response = ser.read(50)
        if response:
            print(f"✅ RÉPONSE 00h: {' '.join([f'{b:02X}' for b in response])}")
        else:
            print("❌ Pas de réponse 00h")
        
        # Test power status avec 00h
        cmd = bytes([0xFE, 0xFE, 0x96, 0x00, 0x18, 0x00, 0xFD])
        print(f"Envoi Power 00h: {' '.join([f'{b:02X}' for b in cmd])}")
        
        ser.write(cmd)
        time.sleep(0.5)
        
        response = ser.read(50)
        if response:
            print(f"✅ RÉPONSE Power: {' '.join([f'{b:02X}' for b in response])}")
        else:
            print("❌ Pas de réponse Power")
        
        # Test avec différents ports COM
        ser.close()
        
    except Exception as e:
        print(f"❌ Erreur COM3: {e}")

def test_other_com_ports():
    """Test d'autres ports COM possibles"""
    print("\n=== Test Autres Ports COM ===")
    
    ports = ['COM1', 'COM2', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8']
    
    for port in ports:
        try:
            ser = serial.Serial(port, 19200, timeout=1.0)
            print(f"✅ {port} ouvert")
            
            # Test simple
            cmd = bytes([0xFE, 0xFE, 0x96, 0x00, 0x03, 0xFD])
            ser.write(cmd)
            time.sleep(0.3)
            
            response = ser.read(20)
            if response:
                print(f"🎉 {port}: RÉPONSE ICOM détectée!")
                print(f"   Données: {' '.join([f'{b:02X}' for b in response])}")
            else:
                print(f"   {port}: Pas de réponse")
            
            ser.close()
            
        except Exception as e:
            print(f"❌ {port}: {e}")

def test_usb_detection():
    """Test de détection USB"""
    print("\n=== Détection USB ===")
    
    import subprocess
    
    try:
        # Lister les périphériques USB
        result = subprocess.run(
            ["wmic", "path", "Win32_PnPEntity", "where", "DeviceID like '%USB%'", "get", "Name,DeviceID"],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print("Périphériques USB détectés:")
            lines = result.stdout.strip().split('\n')
            for line in lines[1:]:  # Skip header
                if line.strip() and 'ICOM' in line.upper():
                    print(f"🎉 ICOM détecté: {line}")
                elif line.strip():
                    print(f"   {line}")
        
    except Exception as e:
        print(f"❌ Erreur détection USB: {e}")

if __name__ == "__main__":
    test_00h()
    test_other_com_ports()
    test_usb_detection()
