{"name": "icom-r8600-controller-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite --host 0.0.0.0 --port 5173 --open", "dev:fast": "vite --host 0.0.0.0 --port 5173 --open --mode development --force", "build": "vite build --mode production", "build:fast": "vite build --mode production --minify esbuild", "preview": "vite preview --host 0.0.0.0 --port 4173", "serve": "python serve.py", "start:ultra": "npm run dev:fast"}, "dependencies": {"axios": "^1.6.0", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "ws": "^8.18.2"}, "devDependencies": {"@vitejs/plugin-react": "^4.1.0", "vite": "^5.0.0"}}