#!/usr/bin/env python3
"""
Test UDP avec adresse E0h (standard PC)
"""

import socket
import time

def test_udp_e0():
    """Test UDP avec adresse E0h"""
    print("=== Test UDP CI-V avec E0h ===")
    print("IP: *************")
    print("Port: 50001")
    print("Adresse contrôleur: E0h")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.settimeout(3.0)
        
        # Test 1: Lecture fréquence avec E0h
        print("\n--- Test 1: Lecture fréquence (E0h) ---")
        cmd = bytes([0xFE, 0xFE, 0x96, 0xE0, 0x03, 0xFD])
        print(f"Envoi: {' '.join([f'{b:02X}' for b in cmd])}")
        
        sock.sendto(cmd, ("*************", 50001))
        
        try:
            response, addr = sock.recvfrom(1024)
            print(f"✅ RÉPONSE E0h: {' '.join([f'{b:02X}' for b in response])}")
            print(f"   Source: {addr}")
        except socket.timeout:
            print("❌ Timeout E0h")
        
        # Test 2: Power status avec E0h
        print("\n--- Test 2: Power status (E0h) ---")
        cmd = bytes([0xFE, 0xFE, 0x96, 0xE0, 0x18, 0x00, 0xFD])
        print(f"Envoi: {' '.join([f'{b:02X}' for b in cmd])}")
        
        sock.sendto(cmd, ("*************", 50001))
        
        try:
            response, addr = sock.recvfrom(1024)
            print(f"✅ RÉPONSE Power: {' '.join([f'{b:02X}' for b in response])}")
        except socket.timeout:
            print("❌ Timeout Power")
        
        # Test 3: Broadcast (00h)
        print("\n--- Test 3: Broadcast (00h) ---")
        cmd = bytes([0xFE, 0xFE, 0x00, 0xE0, 0x03, 0xFD])
        print(f"Envoi: {' '.join([f'{b:02X}' for b in cmd])}")
        
        sock.sendto(cmd, ("*************", 50001))
        
        try:
            response, addr = sock.recvfrom(1024)
            print(f"✅ RÉPONSE Broadcast: {' '.join([f'{b:02X}' for b in response])}")
        except socket.timeout:
            print("❌ Timeout Broadcast")
        
        sock.close()
        
    except Exception as e:
        print(f"❌ Erreur: {e}")

def test_other_ports():
    """Test d'autres ports possibles"""
    print("\n=== Test Autres Ports ===")
    
    ports = [50001, 50002, 4001, 4532, 7300, 7301]
    host = "*************"
    
    for port in ports:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(1.0)
            
            cmd = bytes([0xFE, 0xFE, 0x96, 0xE0, 0x03, 0xFD])
            sock.sendto(cmd, (host, port))
            
            try:
                response, addr = sock.recvfrom(1024)
                print(f"✅ Port {port}: RÉPONSE reçue!")
                print(f"   Données: {' '.join([f'{b:02X}' for b in response])}")
            except socket.timeout:
                print(f"❌ Port {port}: Timeout")
            
            sock.close()
            
        except Exception as e:
            print(f"❌ Port {port}: Erreur {e}")

def test_telnet_connection():
    """Test connexion Telnet (port 23 ouvert)"""
    print("\n=== Test Telnet (Port 23) ===")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5.0)
        
        sock.connect(("*************", 23))
        print("✅ Connexion Telnet réussie")
        
        # Envoyer une commande simple
        sock.send(b"help\r\n")
        
        try:
            response = sock.recv(1024)
            print(f"Réponse Telnet: {response}")
        except:
            print("Pas de réponse Telnet")
        
        sock.close()
        
    except Exception as e:
        print(f"❌ Erreur Telnet: {e}")

if __name__ == "__main__":
    test_udp_e0()
    test_other_ports()
    test_telnet_connection()
