#!/usr/bin/env python3
"""
Test rapide avec adresse E0h standard
"""

import serial
import time

def test_e0():
    """Test avec adresse E0h"""
    print("=== Test avec Adresse E0h Standard ===")
    
    try:
        ser = serial.Serial('COM3', 19200, timeout=2.0)
        print("✅ Port COM3 ouvert")
        
        # Test avec E0h
        cmd = bytes([0xFE, 0xFE, 0x96, 0xE0, 0x03, 0xFD])
        print(f"Envoi E0h: {' '.join([f'{b:02X}' for b in cmd])}")
        
        ser.write(cmd)
        time.sleep(0.5)
        
        response = ser.read(50)
        if response:
            print(f"✅ RÉPONSE E0h: {' '.join([f'{b:02X}' for b in response])}")
        else:
            print("❌ Pas de réponse E0h")
        
        # Test avec DFh
        cmd = bytes([0xFE, 0xFE, 0x96, 0xDF, 0x03, 0xFD])
        print(f"Envoi DFh: {' '.join([f'{b:02X}' for b in cmd])}")
        
        ser.write(cmd)
        time.sleep(0.5)
        
        response = ser.read(50)
        if response:
            print(f"✅ RÉPONSE DFh: {' '.join([f'{b:02X}' for b in response])}")
        else:
            print("❌ Pas de réponse DFh")
        
        # Test broadcast (00h)
        cmd = bytes([0xFE, 0xFE, 0x00, 0xE0, 0x03, 0xFD])
        print(f"Envoi 00h: {' '.join([f'{b:02X}' for b in cmd])}")
        
        ser.write(cmd)
        time.sleep(0.5)
        
        response = ser.read(50)
        if response:
            print(f"✅ RÉPONSE 00h: {' '.join([f'{b:02X}' for b in response])}")
        else:
            print("❌ Pas de réponse 00h")
        
        ser.close()
        
    except Exception as e:
        print(f"❌ Erreur: {e}")

if __name__ == "__main__":
    test_e0()
