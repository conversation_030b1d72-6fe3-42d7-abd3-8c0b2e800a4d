#!/usr/bin/env python3
"""
Test avec adresse DFh (configuration correcte)
"""

import serial
import time

def test_dfh_commands():
    """Test avec adresse DFh"""
    print("=== Test avec Adresse DFh (Correcte) ===")
    
    try:
        ser = serial.Serial('COM6', 19200, timeout=2.0)
        print("✅ COM6 connecté")
        
        # Test 1: Lecture fréquence avec DFh
        print("\n--- Test 1: Lecture Fréquence (DFh) ---")
        cmd = bytes([0xFE, 0xFE, 0x96, 0xDF, 0x03, 0xFD])
        print(f"Envoi: {' '.join([f'{b:02X}' for b in cmd])}")
        
        ser.write(cmd)
        time.sleep(0.5)
        
        response = ser.read(50)
        if response:
            print(f"Réponse: {' '.join([f'{b:02X}' for b in response])}")
            if b'\xFB' in response:
                print("🎉 FB (OK) reçu - SUCCÈS !")
            elif b'\xFA' in response:
                print("❌ FA (ERREUR) reçu")
            else:
                print("⚠️ Réponse inattendue")
        
        # Test 2: Changement fréquence avec DFh
        print("\n--- Test 2: Changement Fréquence 145.500 MHz (DFh) ---")
        # 145500000 Hz en BCD: 00 00 50 45 01
        freq_bcd = [0x00, 0x00, 0x50, 0x45, 0x01]
        cmd = bytes([0xFE, 0xFE, 0x96, 0xDF, 0x05] + freq_bcd + [0xFD])
        print(f"Envoi: {' '.join([f'{b:02X}' for b in cmd])}")
        
        ser.write(cmd)
        time.sleep(0.5)
        
        response = ser.read(50)
        if response:
            print(f"Réponse: {' '.join([f'{b:02X}' for b in response])}")
            if b'\xFB' in response:
                print("🎉 FB (OK) reçu - Fréquence changée !")
            elif b'\xFA' in response:
                print("❌ FA (ERREUR) reçu - Commande rejetée")
        
        # Vérification du changement
        time.sleep(0.5)
        print("\n--- Vérification du Changement ---")
        cmd = bytes([0xFE, 0xFE, 0x96, 0xDF, 0x03, 0xFD])
        ser.write(cmd)
        time.sleep(0.3)
        
        response = ser.read(50)
        if response:
            print(f"Nouvelle fréquence: {' '.join([f'{b:02X}' for b in response])}")
        
        # Test 3: Changement mode FM avec DFh
        print("\n--- Test 3: Changement Mode FM (DFh) ---")
        cmd = bytes([0xFE, 0xFE, 0x96, 0xDF, 0x06, 0x05, 0xFD])  # 0x05 = FM
        print(f"Envoi: {' '.join([f'{b:02X}' for b in cmd])}")
        
        ser.write(cmd)
        time.sleep(0.5)
        
        response = ser.read(50)
        if response:
            print(f"Réponse: {' '.join([f'{b:02X}' for b in response])}")
            if b'\xFB' in response:
                print("🎉 FB (OK) reçu - Mode changé !")
            elif b'\xFA' in response:
                print("❌ FA (ERREUR) reçu")
        
        # Test 4: Power ON avec DFh
        print("\n--- Test 4: Power ON (DFh) ---")
        cmd = bytes([0xFE, 0xFE, 0x96, 0xDF, 0x18, 0x01, 0xFD])
        print(f"Envoi: {' '.join([f'{b:02X}' for b in cmd])}")
        
        ser.write(cmd)
        time.sleep(0.5)
        
        response = ser.read(50)
        if response:
            print(f"Réponse: {' '.join([f'{b:02X}' for b in response])}")
            if b'\xFB' in response:
                print("🎉 FB (OK) reçu - Power ON !")
            elif b'\xFA' in response:
                print("❌ FA (ERREUR) reçu")
        
        ser.close()
        print("\n✅ Test DFh terminé")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")

if __name__ == "__main__":
    print("Test Communication IC-R8600 avec Adresse DFh")
    print("==============================================")
    print("Configuration requise sur IC-R8600:")
    print("MENU → SET → Connectors → CI-V")
    print("- Transceive Address : DFh")
    print()
    
    test_dfh_commands()
    
    print("\n" + "="*50)
    print("RÉSULTAT ATTENDU")
    print("="*50)
    print("Si vous voyez FB (OK) :")
    print("✅ Configuration correcte")
    print("✅ Communication fonctionnelle")
    print("✅ Interface web va fonctionner")
    print()
    print("Si vous voyez encore FA (ERREUR) :")
    print("❌ Vérifiez la configuration CI-V")
    print("❌ Redémarrez l'IC-R8600")
    print("❌ Vérifiez que Transceive Address = DFh")
