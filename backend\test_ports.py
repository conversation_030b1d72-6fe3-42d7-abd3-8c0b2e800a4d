#!/usr/bin/env python3
"""
Test de différents ports UDP pour l'IC-R8600
"""

import socket

def test_multiple_ports():
    """Test plusieurs ports UDP possibles"""
    print("=== Test Ports UDP Multiples ===")
    
    # Ports couramment utilisés par ICOM
    ports_to_test = [50001, 50002, 4532, 4533, 7300, 7301, 50000, 50003]
    
    for port in ports_to_test:
        print(f"\n--- Test Port {port} ---")
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(2.0)
            
            # Commande simple de lecture fréquence
            cmd = bytes([0xFE, 0xFE, 0x96, 0xDF, 0x03, 0xFD])
            print(f"Envoi sur port {port}: {' '.join([f'{b:02X}' for b in cmd])}")
            
            sock.sendto(cmd, ("***********", port))
            
            try:
                response, addr = sock.recvfrom(1024)
                print(f"✅ RÉPONSE REÇUE sur port {port}!")
                print(f"   De: {addr}")
                print(f"   Données: {' '.join([f'{b:02X}' for b in response])}")
                print(f"   Longueur: {len(response)} bytes")
                
                # Ce port fonctionne !
                sock.close()
                return port
                
            except socket.timeout:
                print(f"❌ Timeout sur port {port}")
            
            sock.close()
            
        except Exception as e:
            print(f"❌ Erreur port {port}: {e}")
    
    return None

def test_broadcast():
    """Test en broadcast pour découvrir l'IC-R8600"""
    print("\n=== Test Broadcast Discovery ===")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
        sock.settimeout(3.0)
        
        # Commande de découverte (lecture fréquence)
        cmd = bytes([0xFE, 0xFE, 0x96, 0xDF, 0x03, 0xFD])
        
        # Broadcast sur le réseau 192.3.37.x
        broadcast_addr = "192.3.37.255"
        
        for port in [50001, 50002, 4532]:
            print(f"Broadcast sur {broadcast_addr}:{port}")
            sock.sendto(cmd, (broadcast_addr, port))
        
        # Écouter les réponses
        try:
            while True:
                response, addr = sock.recvfrom(1024)
                print(f"✅ Réponse broadcast de {addr}")
                print(f"   Données: {' '.join([f'{b:02X}' for b in response])}")
        except socket.timeout:
            print("❌ Pas de réponse broadcast")
        
        sock.close()
        
    except Exception as e:
        print(f"❌ Erreur broadcast: {e}")

def main():
    """Fonction principale"""
    print("Test Découverte Ports IC-R8600")
    print("===============================")
    
    # Test des ports multiples
    working_port = test_multiple_ports()
    
    if working_port:
        print(f"\n🎉 PORT TROUVÉ: {working_port}")
        print(f"Modifiez le code pour utiliser le port {working_port}")
    else:
        print("\n❌ Aucun port ne répond")
        
        # Test broadcast
        test_broadcast()
        
        print("\n📋 Actions à vérifier sur l'IC-R8600:")
        print("1. Vérifiez que 'USB/LAN Remote' est sur ON")
        print("2. Cherchez un paramètre 'Control Port' dans les menus LAN")
        print("3. Vérifiez que CI-V Transceive est sur ON")
        print("4. Redémarrez l'IC-R8600 après changement de config")

if __name__ == "__main__":
    main()
