@echo off
echo.
echo ========================================
echo 🚀 ICOM IC-R8600 - DÉMARRAGE PROPRE
echo ========================================
echo.

REM Arrêter tous les processus
echo 🛑 Arrêt de tous les processus...
taskkill /f /im python.exe >nul 2>&1
taskkill /f /im node.exe >nul 2>&1
taskkill /f /im uvicorn.exe >nul 2>&1

REM Attendre que les ports soient libérés
echo ⏳ Attente libération des ports...
timeout /t 5 /nobreak >nul

REM Nettoyer les fichiers cache Python
echo 🧹 Nettoyage cache Python...
cd backend
if exist __pycache__ rmdir /s /q __pycache__
if exist *.pyc del *.pyc
cd ..

echo 📡 Démarrage du backend ultra-rapide...
cd backend
start "ICOM Backend" cmd /k "python main_ultra_fast.py"

echo ⏳ Attente démarrage backend...
timeout /t 8 /nobreak >nul

echo 🌐 Démarrage du frontend Vite...
cd ..\frontend
start "ICOM Frontend" cmd /k "npm run dev"

echo ⏳ Attente démarrage frontend...
timeout /t 10 /nobreak >nul

echo.
echo ✅ SYSTÈME DÉMARRÉ PROPREMENT !
echo.
echo 📊 Interfaces disponibles:
echo    - Backend API: http://localhost:8000
echo    - Frontend Web: http://localhost:5173
echo    - API Docs: http://localhost:8000/docs
echo.
echo 🔗 Ouverture de l'interface...
start http://localhost:5173

echo 🚀 Projet lancé avec succès !
echo.
echo 💡 Note: Le système fonctionne en mode simulation
echo    car l'ICOM n'est pas connecté physiquement.
echo.
pause 