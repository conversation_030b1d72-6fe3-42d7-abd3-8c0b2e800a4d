#!/usr/bin/env python3
"""
Test communication Telnet avec IC-R8600
"""

import socket
import time

def test_telnet_commands():
    """Test des commandes Telnet"""
    print("=== Test Commandes Telnet IC-R8600 ===")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10.0)
        
        print("Connexion à 192.168.1.100:23...")
        sock.connect(("192.168.1.100", 23))
        print("✅ Connexion Telnet établie")
        
        # Lire le message de bienvenue
        welcome = sock.recv(1024)
        print(f"Message de bienvenue: {welcome}")
        
        # Test des commandes
        commands = [
            "help",
            "status", 
            "freq",
            "mode",
            "power",
            "version",
            "info"
        ]
        
        for cmd in commands:
            print(f"\n--- Test commande: {cmd} ---")
            sock.send(f"{cmd}\r\n".encode())
            time.sleep(0.5)
            
            try:
                response = sock.recv(2048)
                print(f"Réponse: {response}")
            except socket.timeout:
                print("Timeout sur la réponse")
        
        sock.close()
        print("✅ Test Telnet terminé")
        
    except Exception as e:
        print(f"❌ Erreur Telnet: {e}")

def test_ci_v_telnet():
    """Test commandes CI-V via Telnet"""
    print("\n=== Test CI-V via Telnet ===")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10.0)
        
        sock.connect(("192.168.1.100", 23))
        
        # Attendre le prompt
        welcome = sock.recv(1024)
        print("Connecté au Telnet")
        
        # Essayer des commandes CI-V en hexadécimal
        ci_v_commands = [
            "FEFE96E003FD",  # Lecture fréquence
            "FEFE96E01800FD",  # Power status
        ]
        
        for cmd in ci_v_commands:
            print(f"\nEnvoi CI-V: {cmd}")
            sock.send(f"{cmd}\r\n".encode())
            time.sleep(1.0)
            
            try:
                response = sock.recv(1024)
                print(f"Réponse: {response}")
            except:
                print("Pas de réponse")
        
        sock.close()
        
    except Exception as e:
        print(f"❌ Erreur CI-V Telnet: {e}")

if __name__ == "__main__":
    test_telnet_commands()
    test_ci_v_telnet()
