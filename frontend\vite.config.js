import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// Configuration ultra-rapide pour ICOM IC-R8600
export default defineConfig({
  plugins: [react({
    // Optimisations React pour performance
    fastRefresh: true
  })],

  // Configuration serveur ultra-rapide
  server: {
    host: '0.0.0.0',
    port: 5173,
    strictPort: true,
    open: true,
    cors: true,
    hmr: {
      port: 5174,  // Port séparé pour HMR
      overlay: false  // Désactiver overlay d'erreur pour performance
    },
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false,
        ws: true,  // Support WebSocket
        timeout: 5000
      },
      '/ws': {
        target: 'ws://localhost:8000',
        ws: true,
        changeOrigin: true
      }
    }
  },

  // Optimisations build ultra-rapides
  build: {
    target: 'esnext',
    minify: 'esbuild',
    sourcemap: false,
    outDir: 'dist',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          ui: ['lucide-react']
        }
      }
    }
  },

  // Optimisations développement
  optimizeDeps: {
    include: ['react', 'react-dom', 'lucide-react'],
    force: true
  },

  // Configuration pour performance maximale
  define: {
    __DEV__: JSON.stringify(process.env.NODE_ENV === 'development'),
    __ULTRA_FAST_MODE__: JSON.stringify(true)
  }
})
