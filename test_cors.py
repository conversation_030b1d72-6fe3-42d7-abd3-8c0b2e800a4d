#!/usr/bin/env python3
"""
Test CORS depuis localhost:8080
"""

import requests

def test_cors():
    """Test CORS avec Origin localhost:8080"""
    print("=== Test CORS depuis localhost:8080 ===")
    
    headers = {
        'Origin': 'http://localhost:8080',
        'Content-Type': 'application/json'
    }
    
    try:
        # Test GET /api/status
        print("Test GET /api/status...")
        response = requests.get("http://localhost:8000/api/status", headers=headers)
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        
        # Vérifier les headers CORS
        if 'Access-Control-Allow-Origin' in response.headers:
            print(f"✅ CORS OK: {response.headers['Access-Control-Allow-Origin']}")
        else:
            print("❌ CORS manquant: Pas d'Access-Control-Allow-Origin")
        
        print(f"Response: {response.json()}")
        
        # Test POST /api/command
        print("\nTest POST /api/command...")
        data = {
            "frequency": 145500000,
            "mode": "FM"
        }
        
        response = requests.post("http://localhost:8000/api/command", json=data, headers=headers)
        print(f"Status Code: {response.status_code}")
        
        if 'Access-Control-Allow-Origin' in response.headers:
            print(f"✅ CORS OK: {response.headers['Access-Control-Allow-Origin']}")
        else:
            print("❌ CORS manquant: Pas d'Access-Control-Allow-Origin")
        
        print(f"Response: {response.json()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

if __name__ == "__main__":
    test_cors()
