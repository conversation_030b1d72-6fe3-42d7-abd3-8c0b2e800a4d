#!/usr/bin/env python3
"""
Test de l'API backend
"""

import requests
import json

def test_status():
    """Test de l'endpoint status"""
    print("=== Test Status ===")
    try:
        response = requests.get("http://localhost:8000/api/status")
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Erreur: {e}")
        return False

def test_command():
    """Test d'envoi de commande"""
    print("\n=== Test Commande ===")
    try:
        data = {
            "frequency": 145500000,
            "mode": "FM"
        }
        
        response = requests.post(
            "http://localhost:8000/api/command",
            json=data
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Erreur: {e}")
        return False

def test_interface():
    """Test de l'interface web"""
    print("\n=== Test Interface Web ===")
    try:
        response = requests.get("http://localhost:8080/simple.html")
        print(f"Status Code: {response.status_code}")
        print(f"Content Length: {len(response.text)}")
        return response.status_code == 200
    except Exception as e:
        print(f"Erreur: {e}")
        return False

if __name__ == "__main__":
    print("Test Complet de l'Application")
    print("=============================")
    
    results = {
        "status": test_status(),
        "command": test_command(),
        "interface": test_interface()
    }
    
    print(f"\n📊 RÉSULTATS:")
    for test, success in results.items():
        status = "✅ OK" if success else "❌ ERREUR"
        print(f"  {test}: {status}")
    
    if all(results.values()):
        print("\n🎉 Tous les tests sont réussis !")
        print("L'interface devrait maintenant fonctionner correctement.")
    else:
        print("\n⚠️ Certains tests ont échoué.")
        print("Vérifiez les logs pour plus de détails.")
