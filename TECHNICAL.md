# Documentation Technique - ICOM IC-R8600 Controller

## 🏗️ Architecture du Système

### Backend (FastAPI)
- **Framework**: FastAPI avec Uvicorn
- **Communication CI-V**: Module `icom_handler.py`
- **Enregistrement audio**: Module `audio_recorder.py` avec SoundDevice
- **API REST**: Endpoints pour contrôle et monitoring
- **WebSocket**: Support pour temps réel (optionnel)

### Frontend (React)
- **Framework**: React 18 avec Vite
- **HTTP Client**: Axios pour les appels API
- **UI**: Interface moderne avec CSS-in-JS
- **Icons**: Lucide React pour les icônes

## 📡 Protocole CI-V

### Format des Commandes
```
FE FE [Adresse Radio] [Adresse Contrôleur] [Commande] [Données] FD
```

### Adresses
- **IC-R8600**: `0x96`
- **Contrôleur**: `0xE0`
- **Préambule**: `FE FE`
- **Postambule**: `FD`

### Commandes Principales

| Fonction | Code | Format | Exemple |
|----------|------|--------|---------|
| Power ON/OFF | `18` | `18 [01/00]` | `FE FE 96 E0 18 01 FD` |
| Set Frequency | `05` | `05 [freq BCD]` | `FE FE 96 E0 05 00 01 45 50 00 FD` |
| Get Frequency | `03` | `03` | `FE FE 96 E0 03 FD` |
| Set Mode | `06` | `06 [mode]` | `FE FE 96 E0 06 05 FD` |
| Get RSSI | `15 02` | `15 02` | `FE FE 96 E0 15 02 FD` |
| RF Gain | `14 02` | `14 02 [gain]` | `FE FE 96 E0 14 02 80 FD` |
| Start Scan | `0E` | `0E 01` | `FE FE 96 E0 0E 01 FD` |
| Stop Scan | `0E` | `0E 00` | `FE FE 96 E0 0E 00 FD` |

### Modes de Modulation

| Mode | Code | Description |
|------|------|-------------|
| LSB | `00` | Lower Side Band |
| USB | `01` | Upper Side Band |
| AM | `02` | Amplitude Modulation |
| CW | `03` | Continuous Wave |
| FM | `05` | Frequency Modulation |
| WFM | `06` | Wide FM |
| CWR | `07` | CW Reverse |
| RTTY | `08` | Radio Teletype |
| RTTYR | `09` | RTTY Reverse |
| PSK | `12` | Phase Shift Keying |
| PSKR | `13` | PSK Reverse |

### Format BCD pour Fréquences
Les fréquences sont encodées en BCD (Binary Coded Decimal) sur 5 bytes, ordre inversé.

Exemple pour 145.500 MHz (145500000 Hz):
- Fréquence: `145500000`
- String: `0145500000`
- BCD: `00 01 45 50 00`
- Commande: `FE FE 96 E0 05 00 01 45 50 00 FD`

## 🔌 Connexions

### Port Série
- **Port**: `/dev/ttyUSB0` (Linux), `COM3` (Windows)
- **Baudrate**: 19200
- **Data bits**: 8
- **Parity**: None
- **Stop bits**: 1
- **Flow control**: None

### UDP (Réseau)
- **Port par défaut**: 50001
- **Protocole**: UDP
- **Format**: Même que série, encapsulé UDP

## 🎵 Enregistrement Audio

### Configuration
- **Format**: WAV 16-bit
- **Fréquence d'échantillonnage**: 48 kHz
- **Canaux**: 1 (mono)
- **Source**: Carte son système

### Types d'Audio
- **AF (Audio Frequency)**: Signal audio démodulé
- **IF (Intermediate Frequency)**: Signal intermédiaire (nécessite démodulation)

## 🌐 API REST

### Endpoints Principaux

#### Contrôle
- `POST /api/command` - Envoyer commande
- `GET /api/status` - État du récepteur

#### Scan
- `POST /api/scan/start` - Démarrer scan
- `POST /api/scan/stop` - Arrêter scan

#### Audio
- `POST /api/audio/start` - Démarrer enregistrement
- `POST /api/audio/stop` - Arrêter enregistrement
- `GET /api/audio/status` - État enregistrement
- `GET /api/recordings` - Liste des fichiers
- `GET /api/recordings/{filename}` - Télécharger fichier
- `DELETE /api/recordings/{filename}` - Supprimer fichier

### Modèles de Données

#### CommandRequest
```json
{
  "frequency": 145500000,
  "mode": "FM",
  "rf_gain": 128,
  "power_on": true
}
```

#### RadioStatusResponse
```json
{
  "frequency": 145500000,
  "mode": "FM",
  "rssi": 120,
  "power_on": true,
  "rf_gain": 128,
  "filter_width": 0,
  "timestamp": "2024-01-01T12:00:00"
}
```

## 🔧 Configuration

### Variables d'Environnement
- `ICOM_PORT`: Port série (défaut: COM3)
- `ICOM_BAUDRATE`: Vitesse série (défaut: 19200)
- `ICOM_USE_UDP`: Utiliser UDP (défaut: false)
- `AUDIO_SAMPLE_RATE`: Fréquence audio (défaut: 48000)

### Fichier config.json
Voir `config.json` pour la configuration complète.

## 🐛 Dépannage

### Problèmes Courants

#### Connexion Série
- Vérifier le port série correct
- S'assurer que l'IC-R8600 est allumé
- Vérifier les permissions sur Linux (`sudo usermod -a -G dialout $USER`)
- Tester avec un autre logiciel CI-V

#### Audio
- Vérifier les périphériques audio disponibles
- Tester l'enregistrement avec un autre logiciel
- Vérifier les permissions microphone

#### API
- Vérifier que le backend est démarré
- Contrôler les logs FastAPI
- Tester avec curl ou Postman

### Logs de Debug
```bash
# Backend
cd backend
python main.py --log-level debug

# Test connexion
python test_icom.py
```

## 📚 Références

- [ICOM IC-R8600 CI-V Reference Guide](https://www.icom.co.jp/)
- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [React Documentation](https://react.dev/)
- [SoundDevice Documentation](https://python-sounddevice.readthedocs.io/)
